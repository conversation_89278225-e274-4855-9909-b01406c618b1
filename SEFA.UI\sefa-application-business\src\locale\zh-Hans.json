{"GLOBAL": {"KEYQUERAY": "快速搜索框输入必须大于2个字符", "SEARCHDATE": "请选择开始时间和结束时间", "_YTS": "已调数", "_KTC": "可退仓", "Summary": "汇总", "Lose": "缺失", "AllLose": "一键缺失", "Unbind": "解绑", "SAPTB": "SAP同步", "upgrade": "升级", "_BCBDY": "保存并打印", "_MB": "模板", "_SBMC": "设备名称", "_DYJ": "打印机", "LText1": "BOM文本", "LText2": "工单文本", "LText3": "物料文本", "LText4": "采购订单文本", "LText5": "工艺路线文本", "LText6": "销售订单文本", "_COMFIRM_HB": "请确认是否合并？", "_SCBQ": "生成标签", "_BZ": "备注", "_WMSLABLE": "追溯碼扫描", "Spotcheck": "点检", "Maintenance": "保养", "Number": "数量", "CF": "重发", "NoFile": "暂无文件", "NoData": "暂无记录", "PDFOnly": "只能上传PDF文件", "CheckImg": "查看图片", "DetialTitle": "扩展信息", "_YC": "移除", "_CK": "查看", "_TS": "提示", "_TB": "同步", "_WC": "完成", "_GD": "更多", "_KS": "开始", "_SQ": "收起", "_CX": "查询", "_XG": "修改", "_KSJY": "开始检验", "_CZ": "重置", "_FZ": "复制", "_PD": "派单", "_TG": "通过", "_BH": "驳回", "_PAICHU": "排除", "_BAOHAN": "包含", "_FZXZ": "复制新增", "_shenqing": "申请", "_SSL": "搜索栏", "_GDCX": "更多查询", "_XZ": "新增", "_XUANZE": "选择", "_KCXZ": "新增库存", "_QR": "确认", "_BJ": "编辑", "_TD": "替换", "_SET": "设置", "_shengcheng": "生成", "_BC": "保存", "_XQ": "详情", "_SC": "删除", "_QC": "清除", "_SQBJ": "申请备件", "_PLXG": "批量修改", "_PLDY": "批量打印", "_PLSC": "批量删除", "_ZKSQ": "展开/收起", "_QD": "确定", "_GB": "关闭", "_ZC": "暂存", "_QX": "取消", "_PJ": "评价", "_PG": "派工", "_QY": "启用", "_JY": "禁用", "_SX": "筛选", "_SX2": "刷新", "_HQ": "获取", "_BTG": "不通过", "_TYPE": "类型", "_BOM": "BOM", "_PCGDCF": "拆分", "_PCGD": "批次工单", "_PCGDXZ": "新增批次工单", "_XFDCS": "下发DCS", "_QDBGBTC": "确定并关闭弹窗", "_MANDATORY": "此项为必填项", "_SELECT": "请选择需要操作的项", "_COMFIRM_GDBG": "请确认是否报工?", "_COMFIRM": "请确认是否删除?", "_COMFIRM_QX": "请确认是否取消?", "_COMFIRM_GB": "请确认是否关闭?", "_COMFIRM_SF": "请确认是否释放?", "_COMFIRM_Reopen": "请确认是否重新打开工单?", "_COMFIRM_GJPC": "请确认是否重新解析构建批次?", "_COMFIRM_BDPF": "请确认是否重新绑定配方?", "_COMFIRM_SSCC": "请输入追溯码", "_COMFIRM_WC": "请确认是否完成?", "_COMFIRM_TL": "请确认是否开始投料?", "_COMFIRM_SC": "请确认是否生成WMS标签?", "_COMFIRM_BF": "请确认是否报废?", "_COMFIRM_TK": "请确认是否退库?", "_COMFIRM_JC": "请确认是否交仓?", "_COMFIRM_BC": "请确认是否保存?", "_COMFIRM_FC": "请确认是否反冲?", "_COMFIRM_RH": "请确认是否退库?", "_COMFIRM_BH": "请确认是否驳回?", "_COMFIRM_DJ": "请确认是否点检?", "_COMFIRM_OVER": "请确认是否结束?", "_COMFIRM_XFDCS": "请确认是否下发DCS?", "_UNBIND": "请确认是否解绑?", "_QRYCXPB": "确定要重新排班吗？", "_G": "共", "_TSJ": "条数据", "_TZD": "跳转到", "_Y": "页", "_BCCG": "保存成功", "_SCCG": "删除成功", "_BHCG": "驳回成功", "_GBCG": "关闭成功", "_XZGD": "选择工单", "_SCTIPS": "数据一经删除将无法恢复，您确定要删除此数据吗？", "_GDJSTS": "工单结束之后将无法重新开启工单，确认结束吗？", "_KEY": "关键字", "_RELEASE": "发布", "_APPROVAL": "审批", "mainProcess": "主流程", "subprocesses": "子流程", "resourceInformation": "资源信息", "_INDEX": "序号", "_ACTIONS": "操作", "StartTime": "开始时间", "EndTime": "结束时间", "_DATE": "日期", "_SAPIMPORT": "SAP导入", "_EXPORT": "导出", "_FILEINPORT": "上传文件", "_IMGINPORT": "上传/查看图片", "_CheckFile": "查看文件", "_EXPORTQR": "导出二维码", "_DR": "导入", "_DC": "导出", "_DCWLXQ": "导出物料需求", "_MBXZ": "模板下载", "Download": "下载", "PrintBarcode": "打印条码", "CreateDate": "创建时间", "CreateUserId": "创建人", "ModifyDate": "最近修改时间", "ModifyUserId": "最近修改人", "noData": "暂无数据", "UnfoldFold": "展开/折叠", "_SM": "扫码", "_XM_BH": "员工姓名/员工工号", "_CXGJZ": "查询关键字", "_YDL": "域登录", "_GROUP": "组别", "_REASON": "原因", "_FS": "发送", "_LINE": "产线", "_AUTOCREATE": "是否自动创建", "_CPBM": "产品编码", "_BDCWB": "绑定长文本", "_WCBTG": "完成并通过"}, "TITLE": {"_WL": "物料", "_DRPF": "导入配方", "_Device": "设备"}, "SCZX": "生产执行", "SCJX": {"GGQZ": "规格群组", "kpi": "kpi计算", "CXCLJS": "产量预算-按产线规格", "LBCLYS": "产量预算-按酱料类别", "YLHSNMB": "原料损耗年目标", "BCSHNMB": "包材损耗年目标", "BCSHNMBSCX": "包材损耗年目标_按生产线", "CXTJLMBZ": "产线停机率目标值"}, "SZGC": "数字工厂", "SIM": {"Mang": "SIM管理", "SIM1": "SIM1", "_SJCG": "升级成功"}, "DFM_XTGL": "系统管理", "DFM_JHGL": "计划管理", "DFM_KPIPZ": "KPI配置", "DFM_ZLGL": "质量管理", "_SBKB": "设备看板", "SBKB": {"SBRW": "设备任务", "GZFXKB": "故障分析看板", "GZFX": "故障分析"}, "KANBAN_GZTJ": {"_GZTJ": "故障统计"}, "DFM_GYGL": {"_GYJCXX": "工序基础信息", "addProcess": "新增工序基础信息", "editProcess": "编辑工序基础信息"}, "DFM_GYLX": {"_NEW": "工艺路线", "_LIST": "工艺路线列表", "_CHART": "流程图", "_GYLX": "工艺路线", "_LXDYCP": "路线对应产品", "_GYLXMX": "工艺路线明细", "_TJGYLXXX": "添加工艺路线信息", "_XGGYLXXX": "修改工艺路线信息", "sectionProcess": "工段流程", "_GC": "工厂", "_LX": "工艺类型", "_LXDM": "路线代码", "_LXMC": "路线名称", "_LXBB": "路线版本", "EffectStart": "生效自", "EffectEnd": "生效至", "Status": "状态", "Description": "描述", "Notes": "备注", "_TJGYLXCP": "添加工艺路线对应产品", "MaterialCode": "成品料号", "MaterialName": "产品名称", "MaterialVersion": "成品料号版本", "Remark": "备注", "_TJGYLXMX": "添加工艺路线明细", "ProcCode": "工序编码", "ProcName": "工序名称", "ProcType": "工序类型", "Unit": "经营单位", "Timb": "工时基准", "Jbcd": "工作类型", "Runm": "运行机器", "Runl": "运行人工", "Setl": "设置人工", "Movd": "搬运小时数", "Qued": "排队小时数", "Version": "版本"}, "DFM_SBLX": {"_SBLX": "设备类型", "_BJSBLX": "编辑设备类型", "CATEGORY_NAME": "设备类型", "_PLACEHOLDER": "请选择上级菜单", "_NODATA": "暂无数据", "DESCRIBE": "描述", "REMARK": "备注"}, "DFM_GXZY": {"_GXZY": "工序资源", "_CKSJSYGYLX": "查看涉及所有工艺路线", "_SZCPHGX": "设置产品号工序", "_DJWH": "刀具BOM维护", "_JJWH": "工装夹具维护", "_FLWH": "辅料维护", "_SBWH": "设备维护", "_CXWH": "机加程序维护", "_TJGZJJXX": "添加工装夹具信息", "FixturesType": "类型", "MaterialCode": "料号", "Quantity": "需要数量", "DeviceTypeName": "设备类型", "Remark": "备注", "_TJFLXX": "添加辅料信息", "Unit": "单位"}, "DFM_CXGYLX": {"_CXGYLX": "产线工艺路线", "add": "新增产线工艺路线", "edit": "编辑产线工艺路线"}, "DFM_GYWJGL": {"_GYWJGL": "工艺文件管理", "_XZGYWJ": "新增工艺文件", "_BJGYWJ": "修改工艺文件", "_CKSJSYGYLX": "查看涉及所有工艺路线", "_SZCPHGX": "设置产品号工序", "_DJWH": "刀具BOM维护", "_JJWH": "工装夹具维护", "_FLWH": "辅料维护", "_SBWH": "设备维护", "_CXWH": "机加程序维护", "_TJGZJJXX": "添加工装夹具信息", "FixturesType": "类型", "MaterialCode": "料号", "Quantity": "需要数量", "DeviceTypeName": "设备类型", "Remark": "备注", "_TJFLXX": "添加辅料信息", "Unit": "单位"}, "DFM_RL": {"_RL": "日历", "_DBBZM": "班组编码", "_GSXX": "公司信息", "_RQ": "日期", "_SZRL": "设置日历", "_QX": "全选", "_SCRL": "删除日历", "_GD": "更多", "_BCGL": "班次管理", "_BZGL": "班组管理", "_YZMS": "运转模式", "_DQY": "当前月", "_BCSJ": "班次时间", "_MC": "名称", "_BM": "部门", "_CX": "产线", "_JC": "简称", "_XH": "序号", "_BZ": "备注", "_CZ": "操作", "_BCS": "班次数", "_BZS": "班组数", "_JCXX": "基础信息", "_BCSZ": "班次设置", "_SLBZ": "首轮值班班组", "_XLBZ": "下轮值班班组", "_TJBC": "添加班次", "_TJBZ": "添加班组", "_TJYZMS": "添加运转模式", "_BC": "班次", "_BZU": "班组", "_YF": "月份", "_XZBZ": "选择班制", "_KSRQ": "开始日期", "_JSRQ": "结束日期", "_DBZQ": "倒班周期", "_SYB": "上一步", "_XYB": "下一步", "_WC": "完成", "_GB": "关闭", "_XZBC": "选择班次", "_XZBZU": "选择班组", "_ZWSJ": "暂无数据", "_KSSJ": "开始时间", "_JSSJ": "结束时间", "_BJRL": "编辑日历", "_XXKSSJ": "休息开始时间", "_XXJSSJ": "休息结束时间", "_XXSJ": "休息时间", "_BZMC": "班组名称", "_BZJC": "班组简称", "_BCMC": "班次名称", "_BCJC": "班次简称", "_XZSYTBDGD": "选择所要同步的工段", "_XZGDKBX": "选择工段(可不选)"}, "DFM_GZRL": {"_GZRL": "工作日历", "_WLMX": "物理模型"}, "DFM_PRINT": {"title": "打印", "country": {"table": {"Description": "描述", "CultureCode": "语言"}, "monthOverride": {"Month": "月份", "Month Override": "月覆写"}}, "labelFomart": {"table": {"Description": "描述", "Country": "国家语言", "LotPrefix": "批次前缀", "LotSuffix": "批次后缀", "DayFormatPrefix": "日期格式前缀", "DayFormat": "日期格式", "DayFormatSuffix": "日期格式后缀", "MonthFormatPrefix": "月份格式前缀", "MonthFormat": "月份格式", "MonthFormatSuffix": "月份格式后缀", "YearFormatPrefix": "年份格式前缀", "YearFormat": "年份格式", "YearFormatSuffix": "年份格式后缀", "IsUpperCase": "是否为大写", "DayFormatPrefix2": "日期格式前缀2", "DayFormat2": "日期格式2", "DayFormatSuffix2": "日期后缀2", "MonthFormatPrefix2": "日期格式前缀2", "MonthFormat2": "月份格式2", "MonthFormatSuffix2": "乐器格式后缀2", "YearFormatPrefix2": "年份格式前缀2", "YearFormat2": "年份格式2", "YearFormatSuffix2": "年份格式后缀2", "MonthFormatOffset": "月份格式偏移", "DayFormatOffset": "日期格式偏移"}, "form": {"Description": "描述", "Country": "国家语言", "LotPrefix": "批次前缀", "LotSuffix": "批次后缀", "DayFormatPrefix": "日期格式前缀", "DayFormat": "日期格式", "DayFormatSuffix": "日期格式后缀", "MonthFormatPrefix": "月份格式前缀", "MonthFormat": "月份格式", "MonthFormatSuffix": "月份格式后缀", "YearFormatPrefix": "年份格式前缀", "YearFormat": "年份格式", "YearFormatSuffix": "年份格式后缀", "IsUpperCase": "是否为大写", "DayFormatPrefix2": "日期格式前缀2", "DayFormat2": "日期格式2", "DayFormatSuffix2": "日期后缀2", "MonthFormatPrefix2": "日期格式前缀2", "MonthFormat2": "月份格式2", "MonthFormatSuffix2": "乐器格式后缀2", "YearFormatPrefix2": "年份格式前缀2", "YearFormat2": "年份格式2", "YearFormatSuffix2": "年份格式后缀2", "MonthFormatOffset": "月份格式偏移", "DayFormatOffset": "日期格式偏移"}}, "labelSize": {"table": {"Size": "数量"}}, "labelTemplate": {"table": {"Code": "编码", "Description": "描述", "Data": "数据", "PrinterClassName": "打印机ID", "TemplateClassName": "模版格式ID", "Variant": "变体", "IsEditable": "是否可编辑", "PreviewConfig": "预览配置", "IsDefault": "是否为默认", "LabelSizeName": "标签尺寸ID", "PreviewCommand": "命令预览", "FileExtension": "文件扩展", "FileNamingType": "文件命名类型"}, "form": {"Code": "编码", "Description": "描述", "Data": "数据", "PrinterClass": "打印机ID", "TemplateClassId": "模板类型", "Variant": "变体", "IsEditable": "是否可编辑", "PreviewConfig": "预览配置", "IsDefault": "是否为默认", "LabelSize": "标签尺寸ID", "PreviewCommand": "命令预览", "FileExtension": "文件扩展", "FileNamingType": "文件命名类型"}}, "LabelPrinter": {"table": {"Code": "编码", "EquipmentName": "设备ID", "PrinterClassName": "打印机类型ID", "Description": "描述", "Status": "状态", "AllowManualPrint": "允许人工打印", "Type": "类型", "SequenceType": "序列类型", "LabelSizeName": "标签尺寸ID"}, "form": {"Code": "名称", "Equipment": "设备", "PrinterClass": "打印类型", "Description": "描述", "Status": "状态", "DefaultLabelCount": "Status", "AllowManualPrint": "允许手动打印", "Type": "类型", "SequenceType": "生成规则", "LabelSize": "标签大小"}}, "LabelPrinterMapping": {"table": {"EquipmentName": "设备", "TemplateName": "模板", "PrinterName": "打印机", "IsDefault": "默认"}, "form": {"Equipment": "设备", "Printer": "打印机", "IsDefault": "是否默认", "DefaultLabelCount": "默认打印张数", "Template": "模板"}}, "LabelPrintHistory": {"table": {"LotName": "批次", "SublotName": "子批次", "Data": "数据", "PrinterName": "打印机", "TemplateName": "模版", "SourceEquipmentName": "源设备", "BatchPalletName": "批次托盘ID", "LogType": "Log类型", "DownloadStatus": "下载状态", "ProductionOrderName": "工单", "Board": "模版"}}, "LabelEquipmentPrinter": {"table": {"EquipmentName": "打印设备", "PrinterName": "打印机", "IsDefault": "是否默认", "DefaultLabelCount": "默认标签数量", "TemplateName": "模版", "MaterialDefinitionName": "物料定义", "MaterialGroupName": "物料组别", "MaterialClassName": "物料批次"}}}, "EnergyConfiguration": {"table": {"EquipmentName": "模块名称", "Name": "名称", "Description": "描述", "Code": "编码", "Categroy": "种类", "SourceType": "方式", "SumType": "计数类型", "Unit": "单位", "Value": "值"}}, "DFM_YHGL": {"HRUSER": "请选择HR用户，加载用户信息", "_YHGL": "用户管理", "_YHXM": "用户姓名", "_GSBM": "公司部门", "_TJYH": "添加用户", "_XZZZ": "选择组织", "LoginName": "登录名", "UserNo": "员工号", "UserName": "用户姓名", "Email": "邮箱", "Age": "年龄", "Sex": "性别", "Birth": "生日", "Tel": "电话", "Remark": "备注", "_MM": "密码", "_ZT": "状态", "_NAN": "男", "_NV": "女", "_QY": "启用", "_JY": "禁用"}, "DFM_JSGL": {"_JSGL": "角色管理", "_QXFP": "权限", "_GJZ": "关键字", "_JSANDYH": "角色名称/用户名称", "_BJJS": "编辑角色", "_QY": "启用", "_JY": "禁用", "Name": "角色名称", "Enable": "状态", "Remark": "描述", "_DQJS": "当前角色", "_YHLB": "用户列表", "_FPYH": "分配用户", "_FPQX": "分配权限", "_XZQX": "选择权限", "_QYCG": "启用成功", "_JYCG": "禁用成功", "_TJJSBDYH": "添加角色绑定用户", "_XZTJYH": "请选中要添加的用户", "_FPSBZ": "分配设备组"}, "DFM_MBGL": {"_MBGL": "模板管理", "_XZMB": "新增模板", "_MBSJ": "模板设计", "_QY": "启用", "_JY": "禁用"}, "DFM_DATAAPI": {"_DATAAPI": "数据API"}, "DFM_TMGL": {"_TMGL": "条码管理", "ckm": "仓库码", "bzm": "班组码"}, "DFM_SYFZ": {"_SYFZ": "使用辅助"}, "DFM_CZRZ": {"_CZRZ": "操作日志", "UserName": "操作人", "ServiceName": "服务名", "_KSSJ": "开始时间", "_JSSJ": "结束时间"}, "DFM_DDGL": {"_XJ": "新建", "_BJ": "编辑", "_WL": "物料", "_ZWSJ": "暂无数据", "_DDGL": "订单管理", "_ADJL": "Andon记录", "ActualStartTime": "开始时间", "ActualEndTime": "结束时间", "Status": "状态", "ProductionCode": "物料号", "_DDH": "订单号", "Line": "产线", "_LX": "类型", "_KZZ": "控制者", "_DDY": "调度员", "_ZDSCGD": "自动生成工单", "_SDSCGD": "手动生成工单", "_QXGD": "取消工单", "SapWoCode": "SAP生产订单号", "ProductionName": "物料名称", "Stage": "阶段", "LockStatus": "是否锁定", "PlanDate": "计划日期", "Type": "订单类型", "PlanQty": "计划数量", "RoutingId": "返工路线", "_FSTZ": "发送通知", "_GBBJ": "关闭报警"}, "DFM_GDGL": {"_Tips": "计划数量不能小于{num}", "_GJZ": "关键字", "_BC": "班次", "_JHSLXG": "计划数量修改", "_SH": "审核", "_HB": "合并", "_CF": "拆分", "_JHSL": "计划数量", "_CFSL": "拆分数量", "_QXZHBD": "请选择合并到", "SapWoCode": "SAP生产订单号", "WoCode": "WO单号", "ProductionCode": "物料号", "ProductionLineCode": "产线", "LineName": "工段", "PlanDate": "计划日期", "Shift": "班次", "Status": "状态", "PlanStartTime": "计划开始时间", "PlanEndTime": "计划结束时间", "PlanQty": "计划数量", "ActualStartTime": "实际开始时间", "ActualEndTime": "实际结束时间", "QuantityQty": "良品数量", "UnquantityQty": "次品数量"}, "DFM_SCGDGTT": {"_SCGDGTT": "生产工单甘特图", "_RQ": "日期", "_DDH": "订单号", "_BC": "班次", "_SAPDDH": "SAP订单号", "_GDH": "工单号", "ProductionCode": "物料号", "ProductionLineCode": "产线", "PlanDate": "计划时间"}, "DFM_SCJDKB": {"_SCJDKB": "生产进度看板", "_DDH": "订单号", "_GD": "工单", "_GDU": "工段", "_ZWSJ": "暂无数据"}, "DFM_SCGD": {"_SCGD": "生产工单"}, "DFM_YWKB": {"_YWKB": "运维看板", "_GDKB": "工单看板", "_GGSBKB": "公共设备看板"}, "DFM_JYLXTY": {"_SJXMWH": "首检项目维护", "_XJXMWH": "巡检项目维护", "_CJXMWH": "抽检项目维护", "_TJXG": "添加/修改", "_SCTP": "上传图片", "_ZWTP": "暂无图片", "_PZ": "拍照", "_SBTJ": "设备调机", "_QXZYY": "请选择原因", "_SB": "设备", "InspectionSheet": "检验单号", "TestItem": "检验项目", "ProductionCode": "物料号", "ProductionName": "物料名称", "Minvalue": "最小值", "MeasuredValue": "测量值", "Maxvalue": "最大值", "Standardvalue": "标准值", "SizingStandard": "定性值", "InspectionText": "OK/NG", "PictureFile": "图片管理"}, "DFM_GDCX": {"_GDCX": "工单查询", "_SJXZ": "时间选择", "_ZDYSJFW": "自定义时间范围", "SapWoCode": "SAP生产订单号", "WoCode": "WO单号", "ProductionCode": "物料号", "ProductionLineCode": "产线", "LineName": "工段", "ProductionName": "物料名称", "PlanDate": "计划日期", "Shift": "班次", "Status": "状态", "PlanStartTime": "计划开始时间", "PlanEndTime": "计划结束时间", "PlanQty": "计划数量", "ActualStartTime": "实际开始时间", "ActualEndTime": "实际结束时间", "QuantityQty": "良品数量", "UnquantityQty": "次品数量", "actions1": ""}, "DFM_RGDXQ": {"_RGDXQ": "日工单详情"}, "DFM_NJH": {"_NJH": "年计划", "_QHST": "切换视图", "_KSNF": "开始年份", "_JSNF": "结束年份", "_ZZT": "柱状图", "ProductionCode": "物料号", "ProductionName": "物料名称", "Factory": "工厂", "Year": "年份", "Quantity": "数量", "actions": "操作"}, "DFM_YJH": {"_YJH": "月计划", "_QHST": "切换视图", "_KSYF": "开始月份", "_JSYF": "结束月份", "ProductionCode": "物料号", "ProductionName": "物料名称", "Factory": "工厂", "Floor": "楼层", "LineCoding": "线体编号", "Month": "月份", "Quantity": "数量", "actions": "操作"}, "DFM_RJH": {"_RJH": "日计划", "_QHST": "切换视图", "_KSRQ": "开始日期", "_JSRQ": "结束日期", "ProductionCode": "物料号", "ProductionName": "物料名称", "Factory": "工厂", "Floor": "楼层", "LineCoding": "线体编号", "Days": "日期", "Quantity": "数量", "actions": "操作"}, "DFM_JYXM": {"_JYXM": "检验项目", "_DRMBXZ": "导入模板下载", "_DR": "导入", "_JYJHSDSC": "检验计划手动生成", "_JYJHSC": "检验计划生成", "_PDBZ": "判断标准", "TestItem": "检验项目", "ProductionCode": "物料号", "ProductionName": "物料名称", "Line": "产线", "Segment": "工段", "Units": "工站", "InspectionType": "判定方式", "InspectionCategory": "检验类别", "Frequency": "采样频次", "SampleSize": "样本容量", "Unit": "单位", "StandardValue": "标准值", "Minvalue": "最小值", "Maxvalue": "最大值", "Testtool": "检测工具", "Toolmodel": "量具型号", "Inputmode": "数据输入方式", "IsAuto": "检验方式", "Executor": "执行人", "Status": "是否启用", "Remark": "备注"}, "DFM_JYJH": {"_JYJH": "检验计划", "_ZDSCJYJH": "自动生成检验计划", "TestType": "检验计划类型", "TestItem": "检验项目", "ProductionCode": "物料号", "ProductionName": "物料名称", "Line": "产线", "Segment": "工段", "Units": "工站", "ExecutionTime": "执行时间", "Executor": "执行人", "Status": "是否有效", "_KSSJ": "执行开始时间", "_JSSJ": "执行结束时间"}, "DFM_SJJL": {"_SJJL": "首检记录", "_KSSJ": "开始时间", "_JSSJ": "结束时间", "_GBBJ": "关闭报警", "_CSBJ": "超时报警", "InspectionSheet": "首检单号", "ProductionCode": "物料号", "ProductionName": "物料名称", "Line": "产线", "Segment": "工段", "Units": "工站", "ExecutionTime": "时间", "Executor": "执行人", "Status": "状态"}, "DFM_XJJL": {"_XJJL": "巡检记录", "_KSSJ": "开始时间", "_JSSJ": "结束时间", "_GBBJ": "关闭报警", "_CSBJ": "超时报警", "InspectionSheet": "巡检单号", "ProductionCode": "物料号", "ProductionName": "物料名称", "Line": "产线", "Segment": "工段", "Units": "工站", "ExecutionTime": "时间", "Executor": "执行人", "Status": "状态"}, "DFM_GCCJ": {"_GCCJ": "过程抽检", "InspectionSheet": "抽检单号", "ProductionCode": "物料号", "ProductionName": "物料名称", "_KSSJ": "开始时间", "_JSSJ": "结束时间", "_GBBJ": "关闭报警", "_CSBJ": "超时报警", "Line": "产线", "Segment": "工段", "Units": "工站", "ExecutionTime": "时间", "Executor": "执行人", "Status": "状态"}, "DFM_MJJL": {"_MJJL": "末检记录", "_KSSJ": "开始时间", "_JSSJ": "结束时间", "_MJJLSC": "末检记录生成", "InspectionSheet": "末检单号", "ProductionCode": "物料号", "ProductionName": "物料名称", "Line": "产线", "Segment": "工段", "Units": "工站", "ExecutionTime": "时间", "Executor": "执行人", "Status": "状态", "IsDuty": "到达现场", "IsClose": "Andon状态"}, "DFM_CPGL": {"PlanQty": "计划数量", "PlanDate": "计划日期", "PlanStartTime": "计划开始时间", "PlanEndTime": "计划结束时间", "RoutingId": "返工路线", "SapWoCode": "SAP生产订单号", "Stage": "阶段", "_CPGL": "产品隔离", "_GLCZ": "隔离处置", "_CZJG": "处置结果", "_GLPSJ": "隔离品收集", "InspectionSheet": "隔离单号", "ProductionCode": "物料号", "ProductionName": "物料名称", "ProductionCodeModel": "转标物料号", "Line": "产线", "Segment": "隔离工段", "BatchNo": "批", "Status": "状态", "D1CreaterCode": "隔离人", "PlanIsolationQty": "计划隔离数量", "ActualIsolationQty": "实际隔离数量", "DealReason": "登记", "_FG": "返工"}, "DFM_YCLLFX": {"_YCLLFX": "一次良率分析", "_BJLLFX": "编辑良率分析", "ProductLine": "产线", "ShiftName": "班次", "StartPlanDate": "开始时间", "EndPlanDate": "结束时间"}, "DFM_YCSJTZ": {"_YCSJTZ": "异常事件通知", "_FSTZ": "发送QMS通知", "_YCSJXQ": "异常事件详情"}, "DFM_CXZLJKKB": {"_CXZLJKKB": "产线质量监控看板"}, "DFM_ZLGLKB": {"_ZLGLKB": "质量管理看板"}, "DFM_ZLJKDWPZ": {"_ZLJKDWPZ": "质量监控点位配置", "_XZDWPZ": "新增点位配置", "_BJDWPZ": "编辑点位配置", "_SFSCS": "是否是测试", "_JZWH": "基准维护", "_PYGZWH": "判异规则维护", "_CYZB": "抽样组别", "_JYJG": "检验结果", "actions": "操作", "AreaName": "产线", "Prodouctname": "工段", "Segmentname": "工站", "TargetPoint": "TAG点", "GlueWater": "胶水"}, "DFM_CPCJ": {"_CJ": "拆解", "_SMTM": "扫描条码", "_CPCJ": "次品拆解", "Sn": "SN", "ProductionCode": "物料号", "Line": "产线", "WoCode": "生产工单", "BatchNo": "批", "ErrorDesc": "异常原因", "WorkTime": "拆解时间", "Operator": "拆解人"}, "DFM_BQPZ": {"_BQPZ": "标签配置", "_CIMJB": "SIM 级别", "_BQMC": "标签名称", "_XZBQ": "新增标签", "_BQBM": "标签编码", "_DW": "单位", "_JSFS": "计算方式", "_MS": "描述", "_SFYX": "是否有效"}, "DFM_ZNYWPZ": {"_ZNYWPZ": "智能运维配置", "Factory": "厂区", "ProductSeries": "产品系列", "TypeName": "监控类型", "Building": "楼栋", "Floor": "楼层", "Linename": "产线", "SegmentName": "工段", "StationLinename": "工站", "Device_Name": "设备", "DataTag": "数据标签名称", "DataFrom": "数据来源", "Position": "位置", "Duty_Person": "运维负责人", "Recipient": "接警人", "GroupRecipient": "群接警人", "State": "状态", "DeviceType": "类型"}, "DFM_SBTJJL": {"_SBTJJL": "设备停机记录", "_RQ": "日期", "_CX": "产线", "_GD": "工段", "_GZ": "工站", "_BZ": "班组", "_TJYY": "停机原因", "_TJMX": "添加明细", "_CJJL": "采集记录", "_TZJL": "调整记录"}, "DFM_SJDR": {"_SJDR": "数据导入", "_SLXZ": "示例下载", "_DRMBXZ": "导入模板下载", "_DR": "导入", "_LX": "类型", "_KSRQ": "开始日期", "_JSRQ": "结束日期", "_DRSJLX": "导入数据类型", "_SCSJWJ": "上传数据文件"}, "DFM_BZZY": {"_BZZY": "帮助指引", "_CDMC": "菜单名称", "_QPZ": "去配置", "_TJZL": "添加子类"}, "DFM_BZPZ": {"_BZPZ": "帮助配置", "_MC": "名称"}, "DFM_MXGL": {"Formula": "配方号", "_CIMJB": "SIM 级别", "_MXGL": "模型管理", "_WLMX": "物理模型", "_KPIName": "KPI 名称", "_PLFZ": "批量复制", "_KPIMX": "KPI 模型", "_JCSX": "基础属性", "_KPIGS": "KPI 公式", "_GSTS": "请使用上面键盘编辑KPI公式", "_GSDY": "公式单元", "_MBZ": "目标值", "ProcessCode": "工序", "KPIName": "KPI名称", "KPICode": "KPI编码", "Period": "颗粒度", "Unit": "单位", "Expression": "公式", "EnabledMark": "是否启用", "Description": "描述", "_XZ": "选择", "_MC": "名称", "_LX": "类型", "_BQMC": "标签名称", "_BQBM": "标签编码", "_JSFS": "计算方式", "_SJBQ": "数据标签"}, "DFM_MXLR": {"_CIMJB": "SIM 级别", "_MXLR": "模型录入", "_WLMX": "物理模型", "_KPIMC": "KPI 名称", "_PLLR": "批量录入", "_SJGL": "数据管理", "_SJLR": "数据录入", "_MBZLR": "目标值录入", "SummaryDate": "汇总日期", "LineName": "厂别/基地/产线", "KPIName": "KPI名称", "Period": "颗粒度", "Team": "班组/班次", "UnitName": "公式参数", "TagName": "标签名称", "Value": "参数值", "_JZMBZ": "KPI基准目标值", "AboveTarget": "KPI上限目标值", "BelowTarget": "KPI下限目标值"}, "DFM_SJZD": {"_SJZD": "数据字典", "_WJSC": "上传安装包", "_ZDFL": "字典分类", "_XZZD": "新增字典", "_XGZD": "修改字典", "_XMM": "项目名", "_XMZ": "项目值", "_PX": "排序", "_JC": "简称", "_YC": "颜色", "_YX": "有效", "_BZ": "备注", "_FLGL": "分类管理", "_TJFL": "添加分类", "_XGFL": "修改分类", "_SJ": "上级", "_MC": "名称", "_BH": "编号", "_SX": "树形"}, "DFM_CDGL": {"_CDGL": "菜单管理", "_XZCD": "新增菜单", "_XGCD": "修改菜单", "_SJCD": "上级菜单", "_CDTB": "菜单图标", "_CDMC": "菜单名称", "_CDBM": "菜单编码", "_ANMC": "按钮名称", "_QXZF": "权限字符", "_CDPX": "排序", "_LYDZ": "路由地址", "_CDZT": "菜单状态"}, "DFM_XWLMX": {"_XWLMX": "物理模型(新)"}, "DFM_OPC": {"_OPC": "OPC", "_TagAddress": "标签地址", "_Server": "用户", "_Group": "组别", "_NewServer": "新增服务", "_NewGroup": "新增组别", "_TagName": "标签名称", "_NewTag": "新标签", "_Export": "导出", "_Import": "导入", "_Refresh": "刷新", "_QuickSearch": "快速搜索"}, "DFM_TAGDATA": {"_TAGDATA": "Tag Data"}, "DFM_DWZH": {"_DWZH": "单位转换", "MaterialCode": "物料", "FormUnitName": "源单位名称", "ConvertFormQty": "源单位数量", "ToUnitName": "转换单位名称", "ConvertToQty": "转换单位数量", "EffectiveBeginDate": "生效开始时间", "EffectiveEndDate": "生效结束时间", "Remark": "备注"}, "DFM_WLMX": {"_WLMX": "物理模型", "_XZMX": "新增模型", "_XGMX": "修改模型", "_SJCD": "上级菜单", "_MC": "名称", "_BM": "编码", "_LX": "类型", "_GB": "关闭", "_QY": "启用", "_MS": "简称", "_SXKZ": "扩展属性", "_SXLB": "属性列表", "_TJPZ": "套件配置", "_TJSX": "添加属性", "_XGSX": "修改属性", "_SXBM": "编码", "_SXZ": "值", "_SXBZ": "备注", "_KXTJ": "可选套件", "_YXTJ": "已选套件"}, "DFM_GXWLZY": {"_GXWLZY": "工序物理资源"}, "DFM_WLGXYS": {"_WLGXYS": "物料关系映射"}, "DFM_CXKPIMBSD": {"_CXKPIMBSD": "产线KPI目标设定"}, "DFM_SCKPIMBSD": {"_SCKPIMBSD": "试产KPI目标设定"}, "DFM_TDLGL": {"_TDLGL": "替代料管理", "Production": "产品", "Material": "物料"}, "DFM_BBJCMX": {"_BBJCMX": "报表基础模型配置"}, "DFM_TJPZ": {"_TJPZ": "套件配置", "_XZTJ": "新增套件", "_XGTJ": "修改套件", "_TJMC": "套件名称", "_TJBM": "套件编码", "_TJLX": "套件类型", "_SXKZ": "扩展属性", "_TJSX": "添加属性", "_XGSX": "修改属性", "_SXBM": "编码", "_SXMC": "名称", "_SXZ": "默认值", "_SXBZ": "备注"}, "DFM_ZZJM": {"_ZZJM": "组织建模", "_ZZXX": "组织信息", "_XZZZ": "新增组织", "_XGZZ": "修改组织", "_SJZZ": "上级组织", "_ZZMC": "组织名称", "_ZZBM": "组织编码", "_ZZLX": "组织类型", "_ZZJC": "组织简称", "_DHH": "电话号", "_BZ": "备注", "_SXKZ": "扩展属性", "_SXLB": "属性列表", "_TJPZ": "套件配置", "_TJSX": "添加属性", "_XGSX": "修改属性", "_SXBM": "编码", "_SXZ": "值", "_SXBZ": "备注", "_KXTJ": "可选套件", "_YXTJ": "已选套件"}, "DFM_GCJM": "工厂建模", "DFM_WLGL": {"WLGL": "物料管理", "WLFLGL": "物料分类管理", "_WLFL": "物料分类", "_WLFZ": "物料分组", "Categorycode": "物料组", "UomOfDim": "基本单位", "Version": "版本号", "addMaterial": "新增物料", "editorMaterial": "编辑物料", "Description": "料号/物料描述", "VendorPn": "物料分类", "copyOne": "复制新增", "copy": "复制", "_TZ": "特征", "_GJZ": "关键字", "Code": "编号", "Name": "名称", "traitName": "特征", "Descriptions": "描述", "_BJWLFL": "编辑物料分类"}, "DFM_WLBOMGL": {"_WLH": "物料号", "LHMC": "成品料号/名称", "WLBOMGL": "物料BOM管理", "BomType": "类型", "Version": "版本号", "Factory": "工厂", "selectBom": "请选择BOM", "addBOM": "新增BOM", "editBOM": "编辑BOM"}, "DFM_WLBOMMX": {"WLBOMGL": "BOM明细列表", "addBOMDetail": "新增BOM明细", "editBOMDetail": "编辑BOM明细"}, "DFM_XBHJW": {"XBHJW": "线边货架位", "selecteRacking": "请选择一个线边货架", "equipmentRacking": "货架对应设备机台", "addRacking": "新增线边货架信息", "editRacking": "编辑线边货架信息", "addPosition": "添加线边货架位信息", "editPosition": "编辑线边货架位信息", "addEquipmentRacking": "添加货架对应机台设备信息", "editEquipmentRacking": "编辑货架对应机台设备信息"}, "DFM_XBHJWL": {"XBHJWL": "线边货架位物料", "selectePosition": "请选择一个线边货架位(点击列表或树型结构)", "bindInfo": "线边货架位信息", "addBind": "新增线边货架位", "editBind": "编辑线边货架位"}, "DFM_GWGL": "岗位管理", "DFM_GXGD": "工序工段", "DFM_LCGL": {"_LCGL": "楼层管理", "_LCXX": "楼层信息", "_RYGL": "人员关联"}, "DFM_GYSGL": {"_GYSGL": "供应商管理", "_XZGYS": "新增供应商", "_BJGYS": "编辑供应商", "_GYSDM": "供应商代码", "_GYSMC": "供应商名称", "_SJH": "手机号", "_DZ": "地址", "_WWSC": "委外生产", "_BZ": "备注", "_MS": "描述", "_SXKZ": "属性扩展", "_SXLB": "属性列表", "_TJPZ": "套件配置", "_TJSX": "套件属性", "_XGSX": "修改属性", "_SXBM": "属性编码", "_SXZ": "属性值", "_SXBZ": "属性备注", "_KXTJ": "可选套件", "_YXTJ": "已选套件"}, "DFM_YYSGL": {"_YYSGL": "原因树管理", "_XZYYS": "新增原因树", "_XGYYS": "修改原因树", "_YYSDM": "原因树代码", "_YYSMC": "原因树名称", "_YYSLX": "原因树类型"}, "DFM_YYXXGL": {"_YYXXGL": "原因信息管理", "_XZYYXX": "新增原因信息", "_XGYYXX": "修改原因信息", "_YYXXDM": "原因代码", "_YYXXMC": "原因名称", "_YYXXLX": "原因类型", "_YYXXMS": "描述"}, "DFM_YYMXGL": {"_YYDMANDMC": "原因明细代码/名称", "_YYMXGL": "原因明细管理", "_YYSXX": "原因树信息", "_XZYYMX": "新增原因明细", "_XGYYMX": "修改原因明细", "_YYS": "原因树", "_YYSPX": "排序", "_YYXX": "原因类型", "_YYSJ": "上级", "_YYBZ": "备注"}, "DFM_DWGL": {"_BJDW": "编辑单位", "_DWGL": "单位管理", "Name": "单位名称", "Index": "序号", "Shortname": "单位简称", "TYPENAME": "类型名称", "Enable": "是否启用", "Description": "描述"}, "DFM_WLGYJM": {"BomRatio": "半成品/成品比例", "_GLSB": "关联设备", "_WLGYJM": "物料工艺建模", "add": "新增工艺资源路线", "update": "编辑工艺资源路线", "MaterialId": "产品名称", "RoutingCode": "工艺路线", "Remark": "备注", "Version": "版本", "VersionProp": "版本号不能大于7位", "device": "设备", "fixtures": "工装", "material": "物料", "file": "工艺文件", "param": "工艺参数", "envParam": "工艺环境参数", "staffing": "人员配备", "attributes": "扩展属性", "addDevice": "新增设备", "updateDevice": "编辑设备", "addFixtures": "新增工装夹具", "updateFixtures": "编辑工装夹具", "addMaterial": "新增物料", "updateMaterial": "编辑物料", "MaterialBom": "物料BOM", "materialBOMDetail": "物料BOM明细列表", "addFile": "新增工艺文件", "updateFile": "编辑工艺文件", "addParam": "新增工艺参数", "updateParam": "编辑工艺参数", "addEnvParam": "新增工艺环境参数", "updateEnvParam": "编辑工艺环境参数", "addStaffing": "新增人员配置", "updateStaffing": "编辑人员配置", "updateBasicInfo": "编辑基础信息", "addBasicInfo": "新增基础信息", "basicInfo": "基础信息"}, "DFM_HTRW": {"_HTRW": "后台任务", "_XJRW": "新建任务", "_XGRW": "修改任务", "_ZTRW": "暂停任务", "_KQRW": "开启任务", "_LJZX": "立即执行", "_ZYMC": "作业名称", "_FZ": "分组", "_JG": "间隔（Cron）", "_URL": "ApiUrl", "_HEADERKEY": "header(key)", "_HEADERVALUE": "header(value)", "_QQFS": "请求方式", "_MS": "描述", "_RZ": "日志", "_RZLB": "日志列表", "_KS": "开始日期", "_JS": "结束如期", "_XX": "信息"}, "DFM_GDSPZCJDPZ": {"_GDSPZCJDPZ": "工段收盘站采集点配置", "ProductlineName": "产线", "SegmentName": "工段", "Materialname": "成品物料", "Intag": "产出采集点", "Oktag": "合格产出点", "Islastsegment": "是否为组装工段", "PlcOktag": "PLC产出TAG点", "Enable": "是否启用"}, "DFM_SLDPZ": {"_SLDPZ": "上料点配置", "ProductlineName": "产线", "SegmentName": "工段", "ProcessName": "工站", "MaterialName": "成品物料", "MaterialType": "物料类别", "TagId": "采集点位", "Uom": "单位", "Tagert": "目标利用率", "BomQty": "单位用量", "Price": "单价", "Seq": "排序", "ComponentClass": "组件类别", "ComponentName": "组件物料", "Enable": "是否启用"}, "TRACE_SCGL": "生产管理", "TRACE_KGJC": {"_KGJC": "开工检查", "_KGQTJC": "工单齐套检查", "_RYYDG": "人员已到岗", "_SBYDJ": "设备已点检", "_GDWLCZ": "工单物料充足", "_GZXHJSLCZ": "工装型号及数量充足", "_SOPWJYXF": "SOP文件已下发", "_CLYQYXZ": "测量仪器已校准", "_CT": "产线", "_GD": "工段", "_GDH": "工单号", "_CPLH": "产品料号", "_BC": "班次", "_KSSJ": "开始时间", "_JSSJ": "结束时间"}, "TRACE_GDGL": {"_GDGL": "工单管理", "_XZCX": "选择产线", "_XZGD": "选择工段", "_KGJC": "开工检查", "_GDKS": "工单开始", "_CJPC": "创建批次", "_GDZT": "工单暂停", "_GDZZ": "工单终止", "_GDJS": "工单结束", "_GDJD": "工单结单", "_BLLR": "不良录入", "_FGCPLR": "返工产品录入", "FullLineName": "线体", "TeamName": "班组", "WoCode": "工单号", "MaterialCode": "产品料号", "Status": "状态", "Shift": "班次", "_KSSJ": "开始时间", "_JSSJ": "结束时间", "_CJSJ": "创建时间", "MaterialDescription": "半成品描述", "SapWoProductionCode": "成品物料", "pcNumbers": "批次数量", "PrintTime": "打印时间", "WoSumBatchQuantity": "成批量(个)", "WoConfirmBatchQuantity": "确认量(个)", "Reason": "关单原因", "_SFGDKS": "是否工单开始？", "_SFZTGD": "是否确认暂停工单？", "_SFZZGD": "是否确认终止工单？", "_QXZGD": "请选择工单", "_PCXQ": "批次详情", "_DY": "打印", "_DYLCK": "打印流程卡"}, "TRACE_SlGL": {"_JSXLCZTS": "你正在进行下料操作！", "_JSXL": "下料", "_SL": "上料", "_PC": "批次", "_WLH": "物料号", "_SLSL": "上料数量", "_SlGL": "上料管理", "_QSGZEWM": "请扫描工站设备二维码", "_QSPCH": "请扫描批次号", "_QSYGH": "请扫描员工号", "_DJSM": "点击扫码", "_CX": "产线", "_GD": "工段", "_GZ": "工站", "_SBH": "设备号"}, "TRACE_JSSXL": {"_JSSXL": "胶水上下料管理"}, "TRACE_XNRK": {"_XNRK": "虚拟入库"}, "TRACE_ZJDTBD": {"_ZJDTBD": "载具单体绑定"}, "TRACE_ZJCPBD": {"_ZJCPBD": "载具产品绑定"}, "TRACE_ZJLB": {"_ZJLB": "Box产品码绑定记录"}, "TRACE_SNJXPZ": {"_SNJXPZ": "SN解析配置"}, "TRACE_CZSCZS": {"_CZSCZS": "车载生产追溯"}, "TRACE_JTRYGL": {"_JTRYGL": "机台人员管理"}, "TRACE_SFCCPYS": {"_SFCCPYS": "SFC产品映射", "FullLineName": "产线", "SectionCode": "工段", "ProductName": "物料名称", "SfcProductName": "SFC产品型号", "SfcDbName": "SFC DB名称", "SfcTableName": "SFC Table名称", "SfcSnFormat": "SFC SN格式", "SfcIpAddress": "SFC IP地址", "Remark": "备注"}, "TRACE_CCZY": {"_CCZY": "充磁作业", "_QSGZEWM": "请扫描工站设备二维码", "_QSPCH": "请扫描批次号", "_DJSM": "点击扫码", "_CX": "产线", "_GD": "工段", "_GZ": "工站", "_SBH": "设备号"}, "TRACE_HXZY": {"_QXXNRK": "取消虚拟入库", "_HXZY": "烘箱作业", "_QSGZEWM": "请扫描工站设备二维码", "_QSPCH": "请扫描批次号", "_DJSM": "点击扫码", "_CX": "产线", "_GD": "工段", "_GZ": "工站", "_SBH": "设备号"}, "TRACE_XNCS": {"_XNCS": "性能测试", "_KSJC": "开始检测", "_CSFD": "测试分档", "_ADC1": "A档一批", "_ADC2": "A档二批", "_ADC3": "A档三批", "_BDC1": "B档一批", "_BDC2": "B档二批", "_BDC3": "B档三批", "_CDC": "C档", "_QRWC": "确认完成", "_CSLR": "测试录入", "_QSYLBEWM": "请扫描批次二维码", "_GDH": "工单号", "_PCH": "批次号", "_PCSL": "批次数量", "_CPX": "产线", "_CPLH": "产品料号", "_BC": "班次", "_KSSJ": "开始时间", "_JSSJ": "结束时间"}, "TRACE_CYCS": {"_CYCS": "纯音测试", "_KSJC": "开始检测", "_JCLR": "检测录入", "_QSYLBEWM": "请扫描批次二维码", "_CPX": "产线", "_GDH": "工单号", "_CPLH": "产品料号", "_PCH": "批次号", "_PCSL": "批次数量", "_CPLR": "次品录入", "_PCSYSL": "批次剩余数量", "_BLSL": "不良数量", "_JCWC": "检查完成", "_KSSJ": "开始时间", "_JSSJ": "结束时间"}, "TRACE_BZRK": {"_XZRKD": "下载入库单", "_BZRK": "包装入库", "_CPWL": "成品物料", "_RKRQ": "入库日期", "_SCRKD": "生成入库单"}, "TRACE_WGJC": {"CHECK_COMFIRM": "是否进行检查完成操作?", "_WGJC": "外观检测", "_KSJC": "开始检测", "_JCLR": "检测录入", "_QSYLBEWM": "请扫描批次二维码", "_CPX": "产线", "_GDH": "工单号", "_CPLH": "产品料号", "_PCH": "批次号", "_PCSL": "批次数量", "_CPLR": "次品录入", "_PCSYSL": "批次剩余数量", "_BLSL": "不良数量", "_JCWC": "检查完成", "_KSSJ": "开始时间", "_JSSJ": "结束时间", "_DYSJLCK": "打印送检流程卡"}, "TRACE_PCHB": {"_PCHB": "批次合并", "_HBCS": "合并次数:", "_HBZSL": "合并总数量", "_QSYLBEWM": "请扫描批次二维码", "_CPX": "产线", "_GDH": "工单号", "_CPLH": "产品料号", "_PCH": "批次号", "_KSSJ": "开始时间", "_JSSJ": "结束时间"}, "TRACE_ABDSNCX": {"_ABDSNCX": "SN测试记录查询"}, "TRACE_GDSNCX": {"_GDSNCX": "工单SN查询"}, "TRACE_CXPZ": {"_CXPZ": "产线配置"}, "TRACE_DLZZY": {"_DLZZY": "等离子作业"}, "TRACE_BZ": {"_BZ": "包装", "_DYXH": "打印箱号", "_CPX": "产线", "_QSMEWM": "请扫码箱号二维码"}, "TRACE_CZBZ": {"_CZBZ": "车载包装"}, "TRACE_WLZS": {"_WLZS": "物料追溯", "_PC_SN": "批次号/SN号"}, "TRACE_SCZS": {"_YCLPCM": "原材料批次码", "_PCM": "批次码", "_CPZS": "产品追溯", "_SCZS": "生产追溯", "_GDXX": "工单信息", "_PCXX": "批次信息", "_WLXX": "物料信息", "_RYXX": "人员信息", "_TRXX": "投入信息", "_JSZS": "胶水追溯", "_CCXX": "产出信息", "_JTXX": "机台信息", "_CPGZZS": "产品过站追溯", "_WLZS": "物料追溯", "_ZLGL": "质量信息", "_GDSNCX": "工单SN查询", "_GDH": "工单号", "_PCHSN": "批次号/SN", "_PCH": "批次号", "FullLineName": "产线", "BatchNo": "批", "SapWoCode": "订单号", "WoCode": "工单", "Sn": "SN"}, "TRACE_GXYS": {"_GXYS": "工序映射"}, "TRACE_GDBG": {"_GDBG": "工单报工"}, "TRACE_ZPGZ": {"startTime": "开始时间", "endTime": "结束时间", "_ZPGZ": "装配过站", "_CZCY": "车载外观", "_CZWG": "车载纯音", "_CPGZZS": "产品过站追溯", "gzlb": "过站列表", "czcy": "车载纯音列表", "czwg": "车载外观列表", "qsrt": "请输入条码", "_BLYY": "不良原因", "gxbn": "工序不能为空"}, "TRACE_OQC": {"_OQC": "OQC", "pcmx": "批次明细", "smewm": "扫描二维码", "tj": "添加", "jcjg": "检测结果"}, "TRACE_SNZXCX": {"_SNZXCX": "SN装箱信息查询", "SapWoCode": "订单号", "WoCode": "工单号", "Sn": "SN", "BoxNo": "箱号"}, "TRACE_CPDTBD": {"_CPDTBD": "产品单体绑定", "_CX": "产线", "Sn": "单体SN", "BoxSn": "箱体SN"}, "TRACE_DYMBBD": {"_DYMBBD": "打印模板绑定", "ProductionName": "物料", "PtlName": "模板名称", "Status": "是否启用"}, "TRACE_SCSJBD": {"_SCSJBD": "1525生产数据查询", "Code1": "载具码1/SN", "Code2": "载具码2/SN", "project": "项目", "line": "产线", "equipment": "设备", "section": "工段", "station": "工站", "startTime": "开始时间", "endTime": "结束时间", "_SNZJM": "载具码/SN", "CollectTime": "采集时间"}, "TRACE_EHYCPBD": {"_EHYCPBD": "二合一产品绑定"}, "Andon_ADGL": "Andon管理", "ANDON_BJLXGL": {"_BJLXGL": "报警类型管理"}, "ANDON_BJTZJL": {"_BJTZJL": "报警通知记录查询"}, "ANDON_BJJL": {"isMain": "查询全部告警", "_BJJL": "报警记录查询", "_BJJLCZ": "报警记录处置", "details": "报警详情", "AlarmPic": "告警图片", "close": "关警处理", "maintenance": "机器维修", "upgrade": "升级路线", "upgradeList": "升级路线列表", "alarmList": "报警详情列表"}, "ANDON_DCTJBJ": {"_DCTJBJ": "多次停机报警规则"}, "ANDON_BJCFGZ": {"_BJCFGZ": "报警触发规则管理"}, "ANDON_BJSJGZ": {"_BJSJGZ": "报警升级规则管理"}, "ANDON_BJZY": {"JYK": "经验库", "_BJZY": "Andon报警主页", "xzbj": "新增报警", "SubAlarmType": "二级分类", "ProblemLevel": "问题等级", "areaid": "车间", "ProductLine": "产线", "EquipmentCode": "设备", "AlarmContent": "告警内容", "jjcz": "接警处置", "ProductLineName": "产线", "SubAlarmName": "二级分类", "problemLevelName": "问题等级", "EquipmentName": "设备", "Comment": "关警内容", "shxx": "损耗信息", "ReasonCode": "原因", "Unit": "单位", "ManQuantity": "人工数量", "DEVICE_Quantity": "设备数量", "tishi": "请选择告警记录", "jjqr": "接警确认", "predicttime": "预估完成时间(min)", "DealTypeName": "告警类型", "PostInfo": "通知信息", "PostType": "通知类型", "PostDept": "通知部门", "PostRole": "通知角色", "PostUser": "通知人", "CheckPostUser": "请选择通知人", "DesignatedHandler": "转办人", "AlarmPic": "告警图片", "AlarmVideo": "告警视频"}, "MATERIAL_WLGL": "物料管理", "MATERIAL_MESCKJM": {"_MESCKJM": "MES仓库建模"}, "MATERIAL_CKWLGZ": {"_CKWLGZ": "仓库物料规则", "kcdy": "库存必须大于0", "aqxyyj": "安全库存必须小于预警库存", "aqxyzg": "安全库存必须小于最高库存", "yjdyaq": "预警库存必须大于安全库存", "yjxyzg": "预警库存必须小于最高库存", "zgdyaq": "最高库存必须大于安全库存", "zgdyyj": "最高库存必须大于预警库存"}, "MATERIAL_AGVCK": {"_AGVCK": "AGV仓库建模", "kqwh": "库区维护", "kqsxwh": "库区属性维护", "kqsx": "库区属性"}, "MATERIAL_CKGXJM": {"_CKGXJM": "AGV&仓库关系建模"}, "MATERIAL_CXCKJM": {"_CXCKJM": "产线&仓库关系建模"}, "MATERIAL_CXSLD": {"_CXSLD": "产线上料点管理"}, "MATERIAL_LLZCD": {"_LLZCD": "领料转储单管理"}, "MATERIAL_YLKCGL": {"cxtl": "产线退料", "_YLKCGL": "原料库存管理", "wlkc": "物料库存", "rkjl": "入库记录", "ckjl": "出库记录", "xzjl": "修正记录", "cxjy": "产线结余库存"}, "MATERIAL_YLFXDDY": {"_YLFXDDY": "原料分线单打印", "bnwk": "仓库编码不能为空", "ckbm": "仓库编码"}, "MATERIAL_WIPKCGL": {"_WIPKCGL": "WIP库存管理", "kclb": "库存列表", "kctj": "库存统计", "wlyk": "物料移库", "gxlb": "勾选列表", "yk": "移库", "ykxx": "移库信息"}, "MATERIAL_JSGL": {"_JSGL": "胶水管理", "jslb": "胶水列表", "jszj": "胶水注胶", "fljl": "发料记录", "wzjjb": "未注胶胶管列表", "cjjb": "创建胶管", "wlbms": "物料编码（扫码）", "wlbm": "物料号", "wlpcs": "胶管条码（扫码）", "wlpc": "物料批号", "zsbm": "追溯信息", "bzqx": "保质期(天)", "jbtmsm": "胶管条码(扫码)", "jbtm": "胶管条码", "jbzl": "胶管重量(克)", "zj": "注胶", "sdcj": "手动创建", "lsxq": "历史需求", "bmmc": "物料"}, "SHIFT_RYGL": "人员管理", "SHIFT_RYSJ": "人员数据", "SHIFT_RYSJ_EYZSJ": {"_RYZSJ": "人员主数据", "_MBXZ": "模板下载", "_SJDR": "数据导入", "_XZTX": "选择头像", "_FD": "放大", "_SX": "缩小", "_YXZ": "右旋转", "_ZXZ": "左旋转"}, "SHIFT_RYSJ_RYJJ": {"_RYJJ": "人员计件(暂停)"}, "SHIFT_RYSJ_RYCQ": {"_RYCQ": "人员出勤", "_CJCX": "车间产线", "_SB": "上班", "_XB": "下班", "ConfirmTimes": "工时", "_QRGS": "确认工时", "_UWBKQGSXQ": "UWB考勤工时详情", "_XH": "序号", "_QYMC": "区域名称", "_KSSJ": "开始时间", "_JSSJ": "结束时间", "_SC": "时长", "_ZSC": "总时长"}, "SHIFT_RYSJ_RYPB": {"_CXPB": "重新排班", "_RYCQ": "人员排班", "_RYLB": "人员列表", "_CJCX": "车间产线", "_FPBZ": "分配班组", "_JD": "执行借调", "_XB": "归还"}, "SHIFT_RYSJ_JJBZ": {"Material": "物料编码", "_JJBZ": "计件标准(暂停)", "_CCBZ": "超产标准", "_GWBMXX": "部门岗位信息", "_JJJZXX": "计件基准信息", "_DJJZXX": "单价基准信息"}, "SHIFT_RYSJ_RYCXPZ": {"_RYCXPZ": "人员产线配置", "_MBXZ": "模板下载", "_SJDR": "数据导入", "_DC": "数据导出"}, "TPM_SBGL": "设备管理", "TPM_SBGL_SBGLZY": {"_SBGLZY": "设备管理主页", "_SBZT": "设备状态", "_XXLX": "信息录入", "_SBWH": "设备维护", "_SBBY": "设备保养", "_DB": "待办", "_JXZ": "进行中", "_YB": "已办", "_WH": "维护", "_XQ": "详情"}, "TPM_SBGL_SBTZGL": {"_SXPZ": "属性配置", "_BGLL": "变更履历", "_DJJL": "点检记录", "_BYJL": "保养记录", "_WXJL": "维修记录", "_BJSY": "备件使用", "_SBBH": "设备编号", "_SBMC": "设备名称", "_ZCBH": "资产编号", "_ZT": "状态", "_SBTZGL": "设备台账管理", "_SBFL": "设备分类", "_SBCX": "设备产线", "_SBXX": "设备信息", "_SBSM": "设备说明", "_SBWH": "设备维护", "_SBPJ": "设备配件", "_BPBJQD": "备件", "_SBZB": "设备指标", "_WJ": "文件", "_SBBOM": "BOM", "_SBLL": "履历", "_DYBQ": "打印设备标签"}, "TPM_SBGL_SBFLGL": {"classname": "分类名称", "classcode": "分类编号"}, "TPM_SBGL_SBDBSX": {"_SBDBSX": "设备待办事项"}, "TPM_SBGL_SBYXZT": {"_SBYXZT": "设备运行状态"}, "WLKCGL_WLKCGL": "库存管理", "WLKCGL_WLKCGLMENU": {"Inspection": "质检管理", "YLBQ": "原料标签管理", "LISTING": "库存列表", "ProductionSummary": "产出汇总", "ProductionHistory": "产出历史", "ConsumptionHistory": "消耗历史", "TransferHistory": "转移历史", "MaterialPreparation": "物料制备", "MaterialPreparationBulid": "物料制备创建", "BatchPallets": "批次托盘", "ContainerManagement": "容器管理", "PalletList": "托盘列表", "TLYJC": "投料预检查", "YJCPZ": "预检查配置", "Feeding": "投料管理", "OperatorConsoleListing": "操作员控制台列表", "overview": "工单管理", "PoList": "工单列表"}, "Producting": {"OperatorConsoleListing": "操作员控制台列表"}, "KANBAN_KBGL": "看板管理", "KANBAN_SBKB": {"_SBKB": "设备看板"}, "KANBAN_GCKB": {"_GCKB": "工厂看板"}, "TPM_SBGL_SBBJGL": {"_DR": "导入", "_DC": "导出", "_MBXZ": "模板下载", "_SBBJGL": "设备备件管理", "_BJKCX": "备件库查询", "_PLDY": "批量打印", "_DYBJBQ": "打印备件标签", "_DYLLD": "打印领料单", "_MXJL": "明细记录", "_RK": "入库", "_RKLB": "入库列表", "_SMRK": "扫码入库", "_CK": "出库", "_CKQD": "出库清单", "_CRJL": "出入记录", "_RKJL": "入库记录", "_CKJL": "出库记录", "_ZSL": "总次数", "_RQ": "日期", "_BJCKSZ": "备件仓库设置", "TBKC": "同步库存", "_BJSL": "备件数量"}, "DFM_SBGZDY": {"_SBGZDY": "设备故障定义"}, "DFM_MATERIAL_GROUP": {"MaterialGroupName": "组名称", "Materials": "组内物料数量", "GroupTypeText": "组类型", "Remark": "备注"}, "DFM_MATERIAL_DETAIL": {"MaterialCode": "物料编码", "MaterialName": "物料描述", "Unit": "计量单位"}, "TPM_SBGL_SBWXGD": {"_ZPR": "维修主管", "_WXJL": "维修记录", "_BJ": "备件", "_FWCG": "服务采购", "_GDLY": "工单来源", "_GDLX": "工单类型", "_SJH": "事件号", "_JDR": "接单人", "_SBMC": "设备名称", "_SBH": "设备号", "_ZT": "状态", "_ZCB": "总成本", "_ZGS": "总工时", "_SBWXGD": "设备维修工单", "_WXGDH": "维修工单号", "_KSSJ": "报修开始时间", "_JSSJ": "报修结束时间", "_WWX": "未维修", "_WXZ": "维修中", "_YWX": "已维修", "_QB": "全部", "_CX": "产线", "_GD": "工段", "_LY": "来源", "_BOM": "BOM", "_BJWLH": "备件物料号", "_FZR": "负责人", "_GZYY": "故障原因", "_FZ": "分钟", "_WXSC": "维修时长"}, "TPM_SBGL_SBWXJL": {"_SBWXJL": "设备维修记录", "_WX": "维修", "_TS": "调试", "_SJH": "事件号", "_WXGDH": "维修工单号", "_KSSJ": "报警开始时间", "_JSSJ": "报警结束时间", "_WXMX": "维修明细", "_TSMX": "调试明细", "_WXCB": "维修成本", "_SFCCWZSK": "是否作为案例(存储为知识库)", "_TZBYGZ": "调整保养规则", "_JQBMMC": "机器名称/编码", "_LRLX": "录入类型", "_WXZT": "维修状态", "_ZXRY": "执行人员", "_BJBM": "备件编码", "_YYFX": "原因分析", "_KSGS": "维修时长最小值", "_JSGS": "维修时长最大值", "_DQKC": "当前库存", "_GZMC": "规则名称", "_GZZ": "规则值", "_XGYY": "修改原因"}, "TPM_SBGL_SBBYXM": {"_SBBYXM": "设备保养项目", "XMDM": "项目代码", "XMMC": "项目名称", "XMFL": "项目分类"}, "TPM_SBGL_SBBYJH": {"_SBBYJH": "设备保养计划", "_SBBYRW": "设备保养任务", "_SCBYJH": "生成保养计划", "_SCBYRW": "生成保养任务", "_RWMX": "任务明细", "_CJRW": "创建保养任务", "_XMMX": "项目明细", "_SBQD": "设备清单", "_BYXMQD": "保养项目清单", "_BJMX": "备件明细", "_PLBY": "批量保养"}, "TPM_SBGL_SBBYGZ": {"_SBBYGZ": "设备保养规则", "Isenable": "状态", "_JQMCBM": "机器名称/编码", "MaintainCycle": "保养周期"}, "TPM_SBGL_SBDJXM": {"_SBDJXM": "设备点检项目"}, "TPM_SBGL_SBDJJH": {"InputContextMaintenance": "请输入保养结果", "InputContext": "请输入点检结果", "_BYXMJHKSSJ": "保养项目计划开始时间", "_DJXMJHKSSJ": "点检项目计划开始时间", "_DJXMQD": "点检项目清单", "_SBDJJH": "设备点检任务", "_SCDJJH": "生成点检任务", "_RWMX": "任务明细", "_PLDJ": "批量点检", "_PLFJ": "批量复检", "_DJ": "点检", "_FJ": "复检", "_FD": "复点"}, "TPM_SBGL_SBDJGZ": {"_SBDJGZ": "设备点检规则", "McProject": "点检项目", "MaintainCycle": "点检周期"}, "TPM_SBGL_BJFFGL": {"_bjkm": "备件科目", "_CKDH": "出库单号", "_BJBM": "备件编码", "_BJMC": "备件名称", "_ZT": "状态", "_GLDH": "关联单号", "_CKSJKS": "出库时间开始", "_CKSJJS": "出库时间结束", "_FF": "发放", "_TH": "退回", "_QX": "取消", "_CM": "采买", "_DC": "导出"}, "TPM_SBGL_BJYJCX": {"QueryInventory": "查询库存", "Filtermaterials": "筛选备件", "ChoosePart": "选择备件", "_BJBM": "备件编码", "_BJMC": "备件名称", "_GGXH": "规格型号", "_YJKSSJ": "预警开始时间", "_YJJSSJ": "预警结束时间", "_DC": "导出"}, "TPM_SBGL_WDBX": {"GDH": "工单号", "ConfirmResult": "维修分数", "ConfirmComment": "备注", "_GDLY": "工单来源", "_ZT": "状态", "_GDLX": "工单类型", "_SBMC": "设备名称", "_SBBH": "设备编号", "_BXKSRQ": "报修开始日期", "_BXJSRQ": "报修结束日期", "_GXKSRQ": "更新开始日期", "_GXJSRQ": "更新结束日期", "_DC": "导出"}, "TPM_SBGL_WDGD": {"_GXKSRQ": "更新开始日期", "_GXJSRQ": "更新结束日期", "tjjy": "推荐经验", "wtms": "问题描述", "gjz": "关键字", "_XGGGXX": "修改故障现象", "_GP": "改派", "_GDLY": "工单来源", "_ZT": "状态", "_GDLX": "工单类型", "_SBMC": "设备名称", "_SBBH": "设备编号", "_JDR": "接单人", "_JD": "接单", "_TH": "退回", "_WXJL": "维修记录", "_CJFWD": "创建服务单", "_BXKSRQ": "报修开始日期", "_BXJSRQ": "报修结束日期", "_DC": "导出", "xqyy": "需求原因", "fwnr": "服务内容", "xykxc": "需要看现场", "csr": "抄送人", "bz": "备注", "wxzgss": "维修总工时数（分钟）", "kscl": "开始处理时间", "ksjs": "结束处理时间", "wxsc": "维修时长（h）", "wxgcms": "维修过程描述", "yyfx": "原因分析", "yyfxjl": "原因分析结论", "wxxz": "维修性质", "gzxz": "故障性质", "gzbw": "故障部位", "wxsyby": "维修时已保养", "zwwxjy": "作为维修经验"}, "TPM_SBGL_JLRWGL": {"MeasureNo": "部门内部编号", "AccountAssetNo": "有账资产码", "yxqks": "有效期开始", "yxqjs": "有效期结束", "jdff": "检定方法", "abcfl": "ABC分类", "bhgqd": "不合格清单", "zzwcrq": "最终完成日期", "zrr": "责任人", "lb": "类别"}, "TPM_SBGL_JLQJGL": {"bh": "编号", "mc": "名称", "zt": "状态", "lb": "类别", "jyzt": "检验状态", "jdff": "检定方法", "abcfl": "ABC分类", "yxqfw": "有效期范围", "jyrqks": "检验日期开始", "jyrqjs": "检验日期结束", "zzwcrq": "最终完成日期", "zrr": "责任人", "yxqks": "有效期开始", "yxqjs": "有效期结束", "jyrw": "创建检验任务"}, "TPM_SBGL_SBDXJH": {"_SCX": "生产线", "_ND": "年度", "_YF": "月份", "rwnr": "任务内容", "zzr": "责任人", "dxnr": "大修内容", "jhksrq": "计划开始日期", "jhjsrq": "计划结束日期", "zb": "周别", "zt": "状态", "comment": "备注", "_ZT": "状态", "scdxrw": "生成大修任务", "cjwxgd": "创建维修工单", "bxsb": "报修设备", "gdnx": "工单类型", "gdly": "工单来源", "gzxx": "故障现象", "bxsj": "报修时间", "qwkssj": "期望开始时间", "qwwcsj": "期望完成时间", "jjcd": "紧急程度", "dzzg": "生产主管", "wxzg": "维修主管", "load": "上传图片/视频"}, "TPM_SBGL_FWDGL": {"_CG": "采购", "_CD": "出单", "_YS": "验收", "_DC": "导出", "_yjqr": "用家确认", "AcceptanceResult": "验收意见", "UserConfirmResult": "确认结果", "_LDYS": "领导验收", "_bmqr": "部门确认", "_gcqr": "工厂确认", "wxdh": "维修单号", "fwdh": "服务单号", "sqr": "申请人", "poh": "工单", "sbh": "设备号", "sbmc": "设备名称", "bxlr": "报修内容", "zt": "状态", "jjd": "紧急度", "sqkssj": "申请开始时间", "sqjssj": "申请结束时间", "wxzt": "维修状态", "sftj": "是否停机", "action": "操作"}, "TPM_SBGL_GZZSK": {"_GZZSK": "故障知识库", "_GZYYS": "故障原因树"}, "DFM_SPCZST": {"_SPCZST": "SPC报表", "_DZT": "单值图", "_XNQXT": "性能曲线图", "_CX": "产线", "_GD": "工段", "_GZ": "工站", "_SB": "设备", "_DW": "点位", "_KSSJ": "开始时间", "_JSSJ": "结束时间", "_JYXM": "检验项目"}, "DFM_CZSPCZST": {"_CZSPCZST": "车载SPC报表"}, "cn": "简体中文", "zhcan": "繁体中文", "user": "用户", "login": "登录", "register": "注册", "username": "用户名", "password": "密码", "rule": {"requiredUsername": "请输入用户名", "requiredPassword": "请输入密码"}, "sponsor": "赞助商", "login_account": "登录账户", "home": "首页", "media": "媒体", "dashboard": "工作首页", "$vuetify": {"badge": "徽章", "close": "关闭", "dataIterator": {"noResultsText": "没有符合条件的结果", "loadingText": "加载中……"}, "dataTable": {"Reportingworkorder": {"order": "工单号", "cp": "成品", "cpbm": "成品编码", "cpmc": "成品名称", "pch": "批次号", "scrq": "生产日期", "gg": "规格", "sjslx": "实际数量(箱)", "sjslz": "实际数量(支)", "zzs": "总支数", "tjsl": "调节数量", "ftbgzzs": "反推报工总支数", "bcp": "半成品", "bcpxh": "半成品消耗", "bcpbm": "半成品编码", "bcpmc": "半成品名称", "dzpjzl": "单支平均重量", "bcpcczs": "半成品产出支数", "jlxh": "酱料消耗", "pf": "配方", "pfbm": "配方编码", "pfmc": "配方名称", "jlccsl": "酱料产出数量"}, "Summary": {"order": "工单号", "MaterialCode": "物料编码", "MaterialName": "物料名称", "MaterialBatchNo": "Lot批次号", "BatchNeed": "批次需求量", "ActualConsume": "实际消耗", "ActualProduced": "实际产出", "Difference": "差异", "SentQty": "已发送数量", "SendResult": "等待Sap发送结果", "NotSentQty": "未发送数量", "FailQty": "发送失败数量", "Unit": "单位", "Qty": "数量", "sscc": "追溯码", "batch": "批次"}, "itemsPerPageText": "每页数目：", "DFM_MBGL": {"Index": "序号", "Name": "模板名称", "Value": "模板值", "Type": "模板类型", "attribute": "属性", "TplJson": "模板内容", "Status": "状态", "Remark": "备注", "actions": "操作"}, "DFM_NJH": {"_NJH": "年计划", "_QHST": "切换视图", "_KSNF": "开始年份", "_JSNF": "结束年份", "_ZZT": "柱状图", "_SLG": "数量/个", "_SJ": "时间", "ProductionCode": "物料号", "ProductionName": "物料名称", "Factory": "工厂", "Year": "年份", "Quantity": "数量", "actions": "操作", "actions1": ""}, "DFM_YJH": {"ProductionCode": "物料号", "ProductionName": "物料名称", "Factory": "工厂", "Floor": "楼层", "LineCoding": "线体编号", "Month": "月份", "Quantity": "数量", "actions": "操作", "actions1": ""}, "DFM_RJH": {"ProductionCode": "物料号", "ProductionName": "物料名称", "Factory": "工厂", "Floor": "楼层", "LineCoding": "线体编号", "Days": "日期", "Quantity": "数量", "actions": "操作", "actions1": ""}, "DFM_JYXM": {"TestItem": "检验项目", "ProductionCode": "物料号", "ProductionName": "物料名称", "Line": "产线", "Segment": "工段", "Units": "工站", "InspectionType": "判定方式", "InspectionCategory": "检验类别", "Frequency": "采样频次", "SampleSize": "样本容量", "Unit": "单位", "StandardValue": "标准值", "Minvalue": "最小值", "Maxvalue": "最大值", "Testtool": "检测工具", "Toolmodel": "量具型号", "Inputmode": "数据输入方式", "IsAuto": "检验方式", "Executor": "执行人", "Status": "是否启用", "Remark": "备注"}, "DFM_JYJH": {"TestType": "检验计划类型", "TestItem": "检验项目", "ProductionCode": "物料号", "ProductionName": "物料名称", "Line": "产线", "Segment": "工段", "Units": "工站", "ExecutionTime": "执行时间", "Executor": "执行人", "Status": "是否有效"}, "DFM_SJJL": {"InspectionSheet": "首检单号", "ProductionCode": "物料号", "ProductionName": "物料名称", "Line": "产线", "Segment": "工段", "Units": "工站", "ExecutionTime": "时间", "Executor": "执行人", "Status": "状态", "IsDuty": "到达现场", "IsClose": "Andon状态"}, "DFM_MJJL": {"InspectionSheet": "末检单号", "ProductionCode": "物料号", "ProductionName": "物料名称", "Line": "产线", "Segment": "工段", "Units": "工站", "ExecutionTime": "时间", "Executor": "执行人", "Status": "状态", "IsDuty": "到达现场", "IsClose": "Andon状态"}, "DFM_XJJL": {"InspectionSheet": "巡检单号", "ProductionCode": "物料号", "ProductionName": "物料名称", "Line": "产线", "Segment": "工段", "Units": "工站", "ExecutionTime": "时间", "Executor": "执行人", "Status": "状态", "IsDuty": "到达现场", "IsClose": "Andon状态"}, "DFM_GCCJ": {"InspectionSheet": "抽检单号", "ProductionCode": "物料号", "ProductionName": "物料名称", "Line": "产线", "Segment": "工段", "Units": "工站", "ExecutionTime": "时间", "Executor": "执行人", "Status": "状态", "IsDuty": "到达现场", "IsClose": "Andon状态"}, "DFM_CPGL": {"InspectionSheet": "隔离单号", "ProductionCode": "物料号", "ProductionName": "物料名称", "ProductionCodeModel": "转标物料号", "Line": "产线", "Segment": "隔离工段", "BatchNo": "批", "Status": "状态", "D1CreaterName": "隔离人", "D1CreaterCode": "隔离人账号", "PlanIsolationQty": "计划隔离数量", "ActualIsolationQty": "实际隔离数量", "DealReason": "登记", "FailureReason": "隔离失败原因"}, "DFM_YCSJXQ": {"Result": "处罚方式", "DealQty": "数量", "UserCode": "处理人", "DealTime": "处罚日期"}, "DFM_YCLLFX": {"PlanDate": "日期", "ShiftName": "班次", "ProductLine": "产线", "ErrorMsg": "良率分析", "多项": "多项"}, "DFM_YCSJTZ": {"CreateBy": "发起人", "EventTime": "发生时间", "Eventcontent": "事件描述", "UeSource": "来源", "UeType": "事件类型", "UePlace": "发生地", "UeLevel": "事件等级", "ProductionCode": "物料号", "ProductionName": "物料名称", "Stage": "产品阶段", "Line": "产线", "Section": "工段", "Result": "产品隔离", "Status": "是否发送", "D1CreaterName": "隔离人", "D1CreaterCode": "隔离人账号"}, "DFM_CPCJ": {"Sn": "SN", "ProductionCode": "物料号", "Line": "产线", "WoCode": "生产工单", "BatchNo": "批", "ErrorDesc": "异常原因", "WorkTime": "拆解时间", "Operator": "拆解人"}, "DFM_ZLJKDWPZ": {"AreaName": "产线", "Prodouctname": "工段", "Segmentname": "工站", "TargetPoint": "TAG点", "GlueWater": "胶水"}, "DFM_ZLJKDWPZ_B": {"PointName": "点位名", "Parameter": "参数", "TestItem": "检验项目", "EnableMonitoring": "是否开启监控", "MonitoringRule": "管控规则", "GoValue": "Goal值", "actions": "操作"}, "DFM_GDGL": {"SapWoCode": "SAP生产订单号", "WoCode": "WO单号", "ProductionCode": "物料号", "ProductionLineCode": "产线", "LineName": "工段", "PlanDate": "计划日期", "Shift": "班次", "Status": "状态", "PlanStartTime": "计划开始时间", "PlanEndTime": "计划结束时间", "PlanQty": "计划数量", "ActualStartTime": "实际开始时间", "ActualEndTime": "实际结束时间", "QuantityQty": "良品数量", "UnquantityQty": "次品数量", "actions": "操作"}, "DFM_DDANDON": {"Status": "Andon状态", "Type": "异常类型", "ErrorMessage": "报警信息"}, "DFM_DDGL": {"SapWoCode": "SAP生产订单号", "ProductionCode": "物料号", "ProductionName": "物料名称", "Stage": "阶段", "Line": "产线", "LockStatus": "是否锁定", "PlanDate": "计划日期", "ActualStartTime": "开始时间", "ActualEndTime": "结束时间", "Type": "订单类型", "PlanQty": "计划数量", "Status": "状态"}, "DFM_GDCX": {"_GDCX": "工单查询", "SapWoCode": "SAP生产订单号", "WoCode": "WO单号", "ProductionCode": "物料号", "ProductionLineCode": "产线", "LineName": "工段", "ProductionName": "物料名称", "PlanDate": "计划日期", "Shift": "班次", "Status": "状态", "PlanStartTime": "计划开始时间", "PlanEndTime": "计划结束时间", "PlanQty": "计划数量", "ActualStartTime": "实际开始时间", "ActualEndTime": "实际结束时间", "QuantityQty": "良品数量", "UnquantityQty": "次品数量", "actions1": ""}, "DFM_CDGL": {"Category": "分类", "CnName": "菜单名称", "CnCode": "菜单编码", "Icon": "图标", "Seq": "排序", "Code": "权限标识", "Route": "菜单路径", "Enable": "有效", "actions": "操作", "isHide": "展示", "IsKeepAlive": "缓存"}, "DFM_WLGL": {"Index": "序号", "Plant": "厂别", "Code": "物料料号", "NAME": "物料名称", "Version": "版本号", "Description": "物料描述", "Type": "物料分类", "Categorycode": "物料组", "UomOfDim": "基本单位", "attribute": "扩展属性", "IsSpecial": "等离子作业", "Deleted": "是否启用"}, "DFM_WLFL": {"Code": "编号", "Name": "名称", "traitName": "特征", "Description": "描述", "actions": "操作"}, "DFM_WLBOMGL": {"Index": "序号", "MaterialCode": "成品料号", "MaterialName": "成品名称", "BomUsage": "BOM使用标志", "AltBom": "替代BOM", "Quantity": "数量", "Version": "版本", "Uom": "单位", "Status": "状态", "EffectStart": "有效期自", "EffectEnd": "有效期至", "ModifyDate": "最新更新时间", "actions": "操作"}, "DFM_WLBOMMX": {"Index": "序号", "ParentName": "上级物料", "CompoentName": "子物料号", "CompoentCode": "子物料编码", "ParentCode": "上级物料编码", "Conversionrate": "良品率", "ParentQuantity": "父级数量", "CompoentQuantity": "子级数量", "Level": "层级", "ProcCode": "工序", "Quantity": "数量", "ParentUom": "父级单位", "CompoentUom": "子级单位", "EffectStart": "有效期自", "EffectEnd": "有效期至", "AltGroup": "替代料组", "AltItem": "替代料", "AltItemOrder": "替代料排序", "AltUsageRate": "替代料使用可能比率", "MaterialGroup": "物料组", "MaterialDescription": "物料描述", "Remark": "备注", "actions": "操作"}, "DFM_XBHJW": {"Index": "序号", "RackingCode": "货架代码", "RackingName": "货架名称", "Status": "有效标志", "Description": "货架位置描述", "Remark": "备注", "ModifyUserId": "最近更新人", "ModifyDate": "最近更新时间", "BinCode": "货架位代码", "BinName": "货架位名称", "BinDescription": "位置描述", "BinType": "货架位类型", "DeviceCode": "设备代码", "actions": "操作"}, "DFM_XBHJWL": {"Index": "序号", "MaterialCode": "物料代码", "Status": "有效标志", "Remark": "备注", "ModifyUserId": "最近更新人", "ModifyDate": "最近更新时间", "actions": "操作"}, "DFM_SJZD": {"Index": "序号", "ItemName": "项目名", "ItemValue": "项目值", "Address": "简拼", "SortCode": "排序", "ShortName": "简称", "Color": "颜色", "Enable": "有效", "Description": "备注", "actions": "操作"}, "DFM_SJZD_FXLB": {"Index": "序号", "name": "名称", "value": "编号", "SortCode": "排序", "SortTree": "树形", "Enable": "有效", "Remark": "备注", "actions": "操作"}, "DFM_GYLX": {"Index": "序号", "RoutingCode": "工艺路线编码", "RoutingName": "路线名称", "Version": "版本", "RoutingType": "工艺类型", "EffectStart": "生效自", "EffectEnd": "生效至", "Status": "状态", "Description": "描述", "Notes": "备注", "actions": "操作", "MaterialCode": "成品料号", "MaterialName": "产品名称", "MaterialVersion": "成品料号版本", "Remark": "备注", "ProcCode": "工序编码", "ProcName": "工序名称", "ProcType": "工序类型", "Unit": "经营单位", "Timb": "工时基准", "Runm": "运行机器", "Runl": "运行人工", "Setl": "设置人工", "Movd": "搬运小时数", "Qued": "排队小时数"}, "DFM_GYSGL": {"Index": "序号", "SupplierCode": "供应商编码", "SupplierName": "供应商名称", "Tel": "电话", "Address": "地址", "IsOutsourcing": "是否委托生产", "Remark": "备注", "actions": "操作"}, "DFM_GWGL": {"Index": "序号", "Name": "岗位名称", "Type": "岗位类型", "WorkHours": "班制", "Description": "描述", "PostPrice": "维修成本单价/小时", "ModifyDate": "最近修改时间", "ModifyUserId": "最近修改人", "CreateDate": "创建时间", "CreateUserId": "创建人", "actions": "操作"}, "DFM_GXGD": {"Index": "序号", "Processname": "工序名称", "Segmentname": "工段简称", "ModifyDate": "最近修改时间", "ModifyUserId": "最近修改人", "CreateDate": "创建时间", "CreateUserId": "创建人", "actions": "操作"}, "DFM_LCGL": {"Index": "序号", "Code": "楼层号", "Name": "楼层名称", "actions": "操作"}, "DFM_LCGL_XQ": {"code": "编码", "name": "名称", "sort": "排序", "actions": "操作"}, "DFM_WLMX": {"Index": "序号", "EquipmentCode": "编码", "EquipmentName": "名称", "Remark": "简称", "Level": "类型", "Enabled": "是否启用", "attribute": "属性", "actions": "操作", "noActions": ""}, "DFM_WLMX_SXKZ": {"Index": "序号", "PropertyCode": "编码", "PropertyValue": "值", "Remark": "描述", "PropertyName": "属性名称", "Enable": "有效", "actions": "操作"}, "DFM_TJPZ": {"Index": "序号", "ClassName": "套件名称", "ClassCode": "套件编码", "ClassType": "套件类型", "attribute": "属性", "actions": "操作"}, "DFM_WLGXYS": {"Type": "物料组", "Categorycode": "物料分类"}, "DFM_CXKPIMBSD": {"Linename": "产线", "Linecode": "产线编号", "Segmentname": "工段", "LineIndex": "指标", "Value": "指标值", "Description": "描述"}, "DFM_SCKPIMBSD": {"Productline": "产线", "Kpitype": "类型", "Maxvalue": "最大值", "Minvalue": "最小值", "Enable": "是否启用"}, "DFM_TDLGL": {"Enabled": "状态", "ProductionCode": "产品料号", "ProductionName": "产品名称", "GroupCode": "替代组编号", "MaterialCode": "物料号", "MaterialName": "物料描述"}, "DFM_BBJCMX": {"Projectname": "产线", "Productline": "产线编码", "Segmentcode": "工段编码", "Segmentname": "工段", "Processname": "工序", "Devicecode": "设备编码", "Inmaterialcode": "投入物料编码", "Inmaterialname": "投入物料", "Outmaterialcode": "产出物料编码", "Outmaterialname": "产出物料", "Intag": "投入采集点", "Faulttag": "故障采集点", "Oktag": "合格产出采集点", "Ngtag": "NG品采集点", "Isfeed": "是否上料工站", "Ct": "CT", "Ischokepoint": "是否瓶颈工站", "Issegmenttag": "是否工段收盘站", "Isproductlinetag": "是否整线收盘站", "OrderNum": "工站排序", "SegmentSeq": "工段排序", "CtTag": "CT采集点", "Stopstatustag": "设备状态采集点", "Sfcmachineid": "SFC测试机台ID", "Sfcdbname": "SFC数据库名称", "Istestsegment": "是否为测试工段", "Issegmentmaterial": "工段良率计算物料上料点", "Orgct": "原始CT", "Isenabled": "工段是否隐藏", "Enable": "是否启用", "ProductCode": "产品型号", "ProductName": "产品型号名称", "ProductType": "产品类型"}, "DFM_TJPZ_SX": {"Index": "序号", "PropertyCode": "编码", "PropertyName": "名称", "DefaultValue": "值", "Remark": "备注", "Enable": "有效", "actions": "操作"}, "DFM_ZZJM": {"Index": "序号", "Fullname": "组织名称", "Encode": "组织编码", "LEVEL": "组织类型", "Shortname": "组织简称", "Outerphone": "电话号", "Description": "备注", "attribute": "属性", "actions": "操作"}, "DFM_ZZJM_SXKZ": {"Index": "序号", "PropertyCode": "编码", "PropertyValue": "值", "Remark": "描述", "Enable": "有效", "actions": "操作"}, "DFM_SBLX": {"Index": "序号", "CATEGORY_NAME": "设备类型", "PARENT_NAME": "上级", "PARENT_TEST": "上上级", "DESCRIBE": "描述", "REMARK": "备注", "CREATEDATE": "创建时间", "CREATEDATE_NAME": "创建人", "MODIFYDATE": "修改时间", "MODIFYUSERID": "修改人", "actions": "操作"}, "DFM_GYGL": {"ParentId": "工序组", "ProcName": "工序名称", "ProcCode": "工序编码", "ProcType": "工序类型", "IsCreateOrder": "是否生成工单", "StandardTime": "标准加工时间", "StandardUom": "标准加工单位", "PrepareTime": "准备时间", "TransTime": "运输时间", "StandardPeronNum": "标准用工人数", "UnitCapacity": "设计产能", "Description": "描述", "CreateDate": "创建时间", "ModifyDate": "修改时间", "ModifyUserId": "修改人", "actions": "操作"}, "DFM_GXZY": {"Index": "序号", "RoutingName": "工艺路线", "MaterialCode": "产品号", "ProcName": "工序", "ProcType": "工艺类型"}, "DFM_GXZY_JV": {"Index": "序号", "FixturesType": "类型", "MaterialCode": "料号", "Quantity": "需要数量", "DeviceTypeName": "设备类型", "Remark": "备注", "ModifyUserId": "最近更新人", "ModifyDate": "最近更新时间", "actions": "操作"}, "DFM_GXZY_FL": {"Index": "序号", "MaterialCode": "辅料料号", "Quantity": "需要数量", "Unit": "单位", "Remark": "备注", "ModifyUserId": "最近更新人", "ModifyDate": "最近更新时间", "actions": "操作"}, "DFM_CXGYLX": {"Factory": "工厂", "ProdctlineName": "工段", "ProduceType": "类型", "YieldRate": "良率", "DelayTime": "偏移时间", "RoutingName": "工艺路线", "Remark": "备注", "ModifyUserId": "最近更新人", "ModifyDate": "最近更新时间", "actions": "操作"}, "DFM_GYWJGL": {"FileType": "工艺文件类型", "FileNo": "工艺文件编号", "FileVersion": "版本", "FileStatus": "状态", "ProdcutName": "产品名称(一类产品)", "FileName": "工艺文件名称", "Material": "材料", "EffectStart": "有效期开始", "EffectEnd": "有效期终止", "FileSpec": "文档规格", "Remark": "备注", "ModifyUserId": "最近更新人", "ModifyDate": "最近更新时间", "actions": "操作"}, "DFM_YHGL": {"Index": "序号", "CompanyName": "公司", "DepartmentName": "部门", "PostName": "岗位", "LoginName": "登录名", "UserNo": "员工号", "UserName": "用户姓名", "Age": "年龄", "Sex": "性别", "Birth": "生日", "Tel": "电话", "EMAIL": "邮箱", "Status": "是否启用", "Remark": "备注", "actions": "操作"}, "DFM_YYSGL": {"Index": "序号", "ReasontreeCode": "原因树代码", "ReasontreeName": "原因树名称", "ReasontreeType": "原因树类型", "actions": "操作"}, "DFM_YYXXGL": {"Index": "序号", "ReasontreeCode": "原因代码", "ReasontreeName": "原因名称", "ReasontreeType": "原因类型", "Remark": "描述", "actions": "操作"}, "DFM_YYMXGL": {"Index": "序号", "ReasontreeCode": "原因树代码", "ReasontreeName": "原因树名称", "DetailCode": "原因明细代码", "DetailDescription": "原因明细名称", "PartentName": "上级", "DetailLevel": "层级", "Remark": "备注", "actions": "操作"}, "DFM_DWGL": {"Index": "序号", "Name": "单位名称", "Shortname": "单位简称", "TYPENAME": "类型名称", "Enable": "是否启用", "Description": "描述", "actions": "操作"}, "DFM_WLGYJM": {"ParentQuantity": "父级数量", "MaterialName": "物料名称", "ParentUnit": "父级单位", "StandardTime": "标准加工时间", "StandardUom": "标准加工单位", "PrepareTime": "准备时间", "TransTime": "运输时间", "StandardPersonNum": "用工人数", "UnitCapacity": "设计产能", "YieldRate": "良品率", "EquipName": "设备名称", "EquipCode": "设备编码", "MaterialCode": "料号", "FixturesType": "类型", "DeviceType": "设备类型", "Quantity": "数量", "CreateDate": "创建时间", "ProcCode": "工序编号", "Inout": "投入产出", "Unit": "单位", "Factory": "工厂", "FileNo": "工艺文件编号", "FileVersion": "工艺文件版本", "FileName": "工艺文件名称", "Docid": "当前页文件编号", "DocVersion": "当前页具体版本", "DocCode": "当前页图号", "Name": "名称", "Code": "编码", "Standard": "标准值", "UpperLimit": "上限", "LowerLimit": "下限", "UpperUpperLimit": "上上限", "LowerLowerLimit": "下下限", "Uom": "单位", "SORT": "排序", "Enabled": "是否可用", "DutyCode": "职责编码", "DutyName": "职责名称", "DutyType": "人员技能", "TimeBase": "基准工时", "RunNum": "运行人数", "SetNum": "设置人数", "Description": "描述", "Remark": "备注", "EquipmentCode": "编码", "EquipmentName": "名称", "Level": "类型", "Productlinename": "产线", "Segmentname": "工段", "Processname": "工站", "ModifyDate": "最近修改时间", "ModifyUserId": "最近修改人", "actions": "操作"}, "DFM_JSGL": {"Name": "角色名称", "Enable": "状态", "Remark": "描述", "actions": "操作"}, "DFM_DATAAPI": {"ModelName": "名称", "ModelCode": "编号", "HandleSql": "Sql", "Description": "备注"}, "DFM_TMGL": {"Wmname": "仓库编码", "Line": "产线", "Shift": "班组"}, "DFM_SYFZ": {"ItemName": "项目名", "ItemValue": "项目值", "Description": "备注"}, "DFM_CZRZ": {"UserName": "操作人", "ServiceName": "服务名", "UserCode": "操作人工号", "MethodName": "方法名", "BrowserInfo": "浏览器信息", "ClientName": "客户端信息", "ClientIpAddress": "客户端IP", "ReturnValue": "操作结果", "MethodRemark": "操作说明"}, "DFM_BQPZ": {"TagName": "标签名称", "TagCode": "标签编码", "Unit": "单位", "Automatic": "计算方式", "EnabledMark": "有效", "Description": "描述", "actions": "操作"}, "DFM_ZNYWPZ": {"Factory": "厂区", "ProductSeries": "产品系列", "TypeName": "监控类型", "Building": "楼栋", "Floor": "楼层", "Linename": "产线", "SegmentName": "工段", "StationLinename": "工站", "Device_Name": "设备", "DataTag": "数据标签名称", "DataFrom": "数据来源", "Position": "位置", "Duty_Person": "运维负责人", "Recipient": "接警人", "GroupRecipient": "群接警人", "State": "状态", "DeviceType": "类型"}, "DFM_SJDR": {"FileName": "文件名称", "CreateUserId": "导入人", "Remark": "类型", "CreateDate": "导入时间"}, "DFM_MXGL": {"SIMName": "SIM 级别", "LineName": "厂别/基地/产线", "ProcessCode": "工序", "KPIName": "KPI名称", "KPICode": "KPI编码", "Period": "颗粒度", "Unit": "单位", "Expression": "公式", "EnabledMark": "是否启用", "Description": "描述", "actions": "操作"}, "DFM_MXLR_FIRST": {"SIMName": "SIM 级别", "LineName": "厂别/基地/产线", "ProcessCode": "工序", "KPIName": "KPI名称", "KPICode": "KPI编码", "PeriodName": "颗粒度", "Unit": "单位", "Expression": "公式", "Description": "描述", "actions1": ""}, "DFM_MXLR_SECOND": {"UnitName": "公式参数", "TagName": "标签名称", "UnitCode": "标签编码", "Automatic": "采集方式", "Description": "描述", "Type": "类型", "actions": ""}, "DFM_HTRW": {"TaskName": "作业名称", "GroupName": "分组", "LastRunTime": "最后执行时间", "Interval": "间隔(Cron)", "Status": "状态", "Describe": "描述", "ApiUrl": "ApiUrl", "RequestType": "请求方式", "actions": "操作"}, "DFM_GDSPZCJDPZ": {"ProductlineName": "产线", "SegmentName": "工段", "Materialname": "成品物料", "Intag": "产出采集点", "Oktag": "合格产出点", "Islastsegment": "是否为组装工段", "PlcOktag": "PLC产出TAG点", "Enable": "是否启用"}, "DFM_SLDPZ": {"ComponentClass": "组件类别", "ComponentName": "组件物料", "ComponentCode": "组件料号", "ProductlineName": "产线", "SegmentName": "工段", "ProcessName": "工站", "MaterialName": "成品物料", "MaterialType": "物料类别", "TagId": "采集点位", "Uom": "单位", "Tagert": "目标利用率", "BomQty": "单位用量", "Price": "单价", "Seq": "排序", "Enable": "是否启用"}, "TRACE_KGJC": {"WoStatus": "工单状态", "FullLineName": "线体", "CompanyName": "工段", "SapWoCode": "SAP订单", "WoCode": "工单", "ShiftName": "班次", "TeamName": "班组", "MaterialCode": "产品料号", "MaterialDescription": "产品描述", "WoQuantity": "计划量(PCS)", "PlanStartTime": "计划开始", "PlanEndTime": "计划结束", "actions": "操作"}, "TRACE_SlGL": {"PersonnelCode": "员工号", "PersonnelName": "员工名字", "Logindt": "上岗时间", "Logoutdt": "离岗时间", "ProductlineName": "产线", "ProductlineCode": "产线编码", "SegmentName": "工站", "SegmentCode": "工站编码", "UnitName": "设备", "UnitCode": "设备号", "FullLineName": "产品线", "WoCode": "工单", "CompanyName": "工段", "TeamName": "班组", "ShiftName": "班次", "ProcName": "工站", "MaterialCode": "物料号", "MaterialDescription": "物料描述", "MaterialProductBatchNo": "批", "SapWoProductionCode": "成品物料", "MaterialBatchNo": "追溯批号", "BatchQuantity": "上料数量", "FeedUnit": "单位", "ModifyUserId": "操作员", "CreateDate": "上料时间"}, "TRACE_GDGL": {"SapWoProductionCode": "成品物料", "SendSapFlag": "SAP同步状态", "SapWoCode": "SAP订单", "CompanyName": "工段", "ShiftName": "班次", "TeamName": "班组", "FullLineName": "线体", "WoCode": "工单号", "MaterialCode": "半成品料号", "MaterialDescription": "半成品描述", "WoQuantity": "计划量(PCS)", "WoSumBatchQuantity": "成批量(PCS)", "WoConfirmBatchQuantity": "确认量(PCS)", "WoCompleteQuantity": "完工量(PCS)", "Reason": "关单原因", "actions": "操作", "WoStatus": "工单状态", "WoType": "工单类型", "PlanStartTime": "计划开始", "PlanEndTime": "计划结束", "ActualStartTime": "实际开始", "ActualEndTime": "实际结束"}, "TRACE_GDGL_XQ": {"BatchNo": "批", "ShiftName": "班次", "TeamName": "班组", "MaterialCode": "产品料号", "BatchQuantity": "数量(PCS)", "BatchStartTime": "开始时间", "BatchEndTime": "结束时间", "actions": "操作"}, "TRACE_CCZY": {"FullLineName": "产品线", "ProcName": "工序", "CompanyName": "工段", "TeamName": "班组", "ShiftName": "班次", "WoCode": "工单号", "BatchNo": "批", "BatchQuantity": "数量(PCS)", "EquipmentName": "设备名称", "Status": "状态", "BatchStartTime": "作业开始时间", "BatchEndTime": "作业结束时间"}, "TRACE_HXZY": {"ProcName": "工序", "FullLineName": "产品线", "CompanyName": "工段", "TeamName": "班组", "ShiftName": "班次", "WoCode": "工单号", "BatchNo": "批", "BatchQuantity": "批次数量(PCS)", "EquipmentName": "设备", "Status": "状态", "BatchStartTime": "作业开始时间", "BatchEndTime": "作业结束时间"}, "TRACE_XNCS": {"CurrentProcName": "工序", "FullLineName": "产品线", "CompanyName": "工段", "WoCode": "工单号", "TeamName": "班组", "ShiftName": "班次", "MaterialCode": "产品料号", "MaterialDescription": "产品描述", "BatchNo": "批", "BatchQuantity": "批次数量(PCS)", "CurrentProcStatus": "状态", "ModifyDate": "接受时间", "actions": "操作"}, "TRACE_CYCS": {"FullLineName": "产品线", "CompanyName": "工段", "WoCode": "工单号", "TeamName": "班组", "ShiftName": "班次", "MaterialCode": "产品料号", "MaterialDescription": "产品描述", "BatchNo": "批", "BatchQuantity": "批次数量(PCS)", "BatchBadQuantity": "不良数量(PCS)", "TestGrade": "类型", "Status": "状态", "CreateDate": "作业时间"}, "TRACE_BZRK": {"FullLineName": "产线", "MaterialCode": "物料编号", "MaterialDescription": "物料描述", "SheetNo": "入库单号", "SumQuantity": "总数", "BoxCount": "件数", "BookDate": "入库日期"}, "TRACE_WGJC": {"FullLineName": "产品线", "CompanyName": "工段", "TeamName": "班组", "ShiftName": "班次", "WoCode": "工单号", "MaterialCode": "产品料号", "MaterialDescription": "产品描述", "BatchNo": "批", "BatchQuantity": "批次数量(PCS)", "BatchBadQuantity": "不良数量(PCS)", "TestGrade": "类型", "Status": "状态", "CreateDate": "接收时间"}, "TRACE_PCHB": {"FullLineName": "产品线", "CompanyName": "工段", "TeamName": "班组", "ShiftName": "班次", "MaterialCode": "产品料号", "MaterialDescription": "产品描述", "BatchNo": "批", "BatchQuantity": "批次数量(PCS)", "TestGrade": "类型", "CreateDate": "合并时间"}, "TRACE_PCHB_XQ": {"CompanyName": "产线", "MaterialCode": "产品料号", "TeamName": "班组", "ShiftName": "班次", "BatchNo": "批", "RemainQuantity": "批次数量(PCS)", "WoQuantity": "合并数量(PCS)", "TestGrade": "类型", "actions": "操作"}, "TRACE_ABDSNCX": {"ProductLine": "线体", "ProductModel": "型号", "WoCode": "工单", "BatchNo": "批", "Sn": "SN", "SnProductionDate": "生产日期", "SnProductionShiftName": "生产班次", "TestLevel": "测试档次", "TestResult": "测试结果", "ProcessName": "测试机台", "TestTime": "测试时间", "TestFileName": "测试文件名称", "Segment": "测试工段", "Remark": "备注信息"}, "TRACE_GDSNCX": {"WoCode": "工单号", "BatchNo": "批", "Sn": "SN", "ShiftName": "班次", "TeamName": "班组", "FactoryName": "工厂", "FullLineName": "产线", "MaterialCode": "物料编码"}, "TRACE_GXYS": {"ProductCode": "产品料号", "SapProcessCode": "SAP工序编码", "SapProcessName": "SAP工序名称", "MesProcess": "MES工序", "SpecialSetting": "报工特殊配置", "MesProcessCode": "MES工序编码", "MesProcessName": "MES工序名称"}, "TRACE_CXPZ": {"FullLineName": "产品线", "CompanyName": "工段", "MaterialCode": "物料编码", "MaterialDescription": "物料描述", "DefaultBatchQty": "最大成批数(PCS)", "AutoStartStopWo": "自动启停工单", "actions": "操作", "AllowUpdateLastBatchQty": "允许修改尾批数量", "AllowLastBatchQtyOverMax": "允许修改尾批数量超最大成批数量", "AllowCreateBatchType": "手动创建批次类型", "PrintSectionName": "打印工段别名", "MinBatchQty": "最小成批数(PCS)"}, "TRACE_DLZZY": {"Index": "序号", "EquipmentCode": "机台号", "EquipmentName": "机台名称", "MaterialCode": "物料编码", "MaterialName": "物料名称", "MaterialBatchNo": "物料批次号", "MaterialZsBatchNo": "物料追溯批次号", "MaterialBarcode": "物料条码", "StartTime": "作业开始时间", "EndTime": "作业结束时间"}, "TRACE_BZ": {"FullLineName": "产品线", "MaterialCode": "产品编码", "MaterialDescription": "产品名称", "BoxNumber": "箱号", "Quantity": "数量(PCS)", "ShiftName": "班次", "TeamName": "班组", "SendSapFlag": "是否发送SAP", "CreateDate": "扫描时间", "BoxNo": "批次号", "InQty": "数量", "InStorageTime": "扫描时间"}, "TRACE_WLZS": {"BatchNo": "批", "WoCode": "工单号", "Type": "物料批次类型", "TestGrade": "测试等级", "MaterialCode": "物料编码", "MaterialDescription": "物料描述", "BatchQuantity": "批次数量", "BindQuantity": "占用数量", "Unit": "单位", "CreateDate": "批次创建时间", "FeedingTime": "上料时间"}, "TRACE_SCZS_GDXX": {"SapWoCode": "SAP订单", "WoCode": "工单", "ShiftName": "班次", "FullLineName": "线体", "CompanyName": "工段", "MaterialCode": "产品料号", "MaterialDescription": "产品描述", "WoQuantity": "计划量(PCS)", "WoSumBatchQuantity": "成批量(PCS)", "WoConfirmBatchQuantity": "确认量(PCS)", "WoCompleteQuantity": "完工量(PCS)", "PlanStartTime": "计划开始", "PlanEndTime": "计划结束", "ActualStartTime": "实际开始", "ActualEndTime": "实际结束", "Reason": "关单原因"}, "TRACE_SCZS_PCXX": {"CompanyName": "工段", "BatchNo": "批", "MaterialCode": "产品料号", "MaterialDescription": "产品描述", "BatchQuantity": "数量(PCS)", "BatchStartTime": "开始时间", "BatchEndTime": "结束时间"}, "TRACE_SCZS_WLXX": {"WoCode": "工单号", "MaterialCode": "物料料号", "MaterialDescription": "产品描述", "ScadaTagQty": "SCADA计数量", "BomQty": "BOM用量", "ScadaConsumptionQty": "SCADA消耗量", "ConfirmConsumptionQty": "确认消耗量", "MaterialUom": "单位"}, "TRACE_SCZS_ZLGL": {"TestItem": "检验项目", "ProductionCode": "产品编码", "ProductionName": "产品名称", "Line": "产线", "Segment": "工段", "EquipmentName": "工站", "Maxvalue": "最大值", "Minvalue": "最小值", "Standardvalue": "标准值", "MeasuredValue": "测量值"}, "TRACE_SCZS_GDSNCX": {"FullLineName": "产线", "BatchNo": "批", "SapWoCode": "订单号", "WoCode": "工单", "Sn": "SN"}, "TRACE_SCZS_RYXX": {"PersonCode": "操作人", "EquipmentCode": "设备编码", "EquipmentName": "设备名称", "Level": "类型", "UWB": "UWB号牌", "Code": "员工工号", "Name": "员工名称", "JobContent": "岗位", "Duration": "工时", "Shift": "班次"}, "TRACE_ZPGZ": {"CreateDate": "过站时间", "FullLineName": "产品线", "WoCode": "工单号", "MaterialCode": "物料编码", "Sn": "SN", "Sns": "SN(扫码)", "ProcName": "工序", "Result": "结果", "Remark": "备注"}, "TRACE_OQC": {"WoCode": "工单号", "MaterialCode": "料号", "BatchNo": "批", "RemainQuantity": "数量", "actions }": "操作", "name": "检测项目", "cjs": "抽检数", "bls": "不良数", "blyy": "不良原因", "CheckItemName": "检测项目", "Quantity": "数量", "Reason": "不良原因", "MaterialDescription": "物料描述", "BatchQuantity": "数量", "CheckQty": "抽检数", "FullLineName": "产品线", "OqcBatchNo": "OQC批次号", "Result": "结果", "Remark": "备注", "BadQty": "不良数"}, "TRACE_SFCCPYS": {"FullLineName": "产线", "SectionCode": "工段", "ProductName": "物料名称", "SfcProductName": "SFC产品型号", "SfcDbName": "SFC DB名称", "SfcTableName": "SFC Table名称", "SfcSnFormat": "SFC SN格式", "SfcIpAddress": "SFC IP地址", "Remark": "备注"}, "TRACE_SNZXCX": {"FullLineName": "产线", "CurrentProcName": "工站", "SapWoCode": "订单号", "WoCode": "工单号", "Sn": "SN", "BoxNo": "箱号", "MaterialCode": "物料名称", "BatchNo": "装配批次号", "Status": "状态", "BoxTime": "装箱时间"}, "TRACE_CPDTBD": {"Sn": "单体SN", "BoxSn": "Box产品SN", "BindTime": "绑定时间"}, "TRACE_DYMBBD": {"ProductionName": "物料", "PtlName": "模板名称", "Status": "是否启用"}, "ANDON_SET": {"PropertyCode": "编码", "Remark": "属性名称", "PropertyValue": "值", "actions": "操作"}, "ANDON_BJLXGL": {"Enable": "是否启用", "MessagePostTag": "发送告警通知", "OverMessagePostTag": "发送关警通知", "AlarmCode": "编码", "AlarmName": "名称", "ProblemLevel": "问题等级", "DealType": "处置类型", "MessageTemplate": "消息模板", "OverMessageTemplate": "消息关闭模板", "UwbMessageTemplate": "Uwb消息模板", "ParentId": "一级分类", "Icon": "图标", "Sort": "排序", "Description": "描述", "actions": "操作"}, "ANDON_BJTZJL": {"AreaCode": "车间", "areacode": "车间", "ProductLine": "产线", "UnitCode": "工作中心", "EquipmentCode": "设备", "MainAlarmType": "一级分类", "SubAlarmType": "二级分类", "PushType": "推送类型", "Reciver": "接收者", "NoticeTitle": "标题", "NoticeContent": "内容", "Status": "发送状态"}, "ANDON_BJYYFX": {"MainAlarmType": "安灯类型", "SubAlarmType": "报警原因", "Frequency": "数量", "CumulativePercentage": "累计占比（%）", "SingleItemPercentage": "单项占比（%）", "ResponseTime": "平均响应时长（分钟）", "ProcessTime": "平均处理时长（分钟）"}, "ANDON_BJJL": {"IsMain": "是否主告警", "Currentman": "接警人", "EventNo": "事件号", "AreaName": "车间", "AreaCode": "车间", "areacode": "车间", "ProductLineName": "产线", "UnitName": "工作中心", "EquipmentName": "设备", "MainAlarmType": "一级分类", "SubAlarmType": "二级分类", "ProblemLevel": "问题等级", "EventLevel": "等级", "RecordStatus": "当前状态", "AlarmContent": "告警内容", "Comment": "关警说明", "Predicttime": "预估完成时间(分)", "guid": "当前处置人", "Callman": "接警人", "Calldate": "接警时间", "Overdate": "完成时间", "Closeman": "关警人", "Closedate": "关警时间", "Wo": "工单", "Process": "阶段", "ActionType": "描述", "Receiver": "Scada统计量", "StartTimeDiff": "开始时差", "PreTimeDiff": "上一步时差", "Duty": "接警组", "Dutydetail": "安灯岗位", "OutTime": "升级时间（分)", "EquipmentCode": "物料名称", "MainAlarm": "一级分类", "SubAlarm": "二级分类", "times": "排序", "state": "状态", "Currentduty": "负责人", "Total": "已发生次数", "con": "持续时间", "repair_man": "维修人员", "ReasonCode": "故障代码", "CreateDate": "发生时间"}, "ANDON_DCTJBJ": {"ProductionLineCode": "工段", "Times": "停机次数", "Duration": "停机累计时长(分)"}, "ANDON_BJCFGZ": {"Maintype": "一级分类", "Subtype": "二级分类", "Productlinecode": "工段", "Equipmentcode": "设备", "Variable": "指标名称", "Syboml": "比较符", "Standardvalue": "标准值", "Deviationpercent": "偏差百分比(%)"}, "ANDON_BJSJGZ": {"UpgradeType": "升级类型", "MainAlarmType": "一级分类", "SubAlarmType": "二级分类", "EventLevel": "事件等级", "OutTime": "升级时间(分)", "NoterType": "通知类型", "Dutydetail": "通知岗位", "NoticeType": "通知方式", "DealMode": "处置方式", "IsDownAllSend": "是否向下群发", "ResponseLevel": "响应等级", "NoterDepart": "通知角色", "NoterName": "通知人", "Noter": "通知人"}, "MATERIAL_MESCKJM": {"WarehouseCode": "仓库编号", "WarehouseName": "仓库名称", "WarehouseType": "仓库类型", "ProductLine": "产品线", "Abbreviation": "简称", "Floor": "楼层", "Description": "仓库描述", "SapPositionName": "SAP库位", "SapPosition": "SAP库位编码"}, "MATERIAL_CKWLGZ": {"WarehouseCode": "仓库编号", "Linename": "产线", "MaterialName": "物料名称", "WarehouseId": "供料仓库", "WarehouseType": "仓库类型", "MaterialCode": "物料编码", "Unit": "计量单位", "Safevalue": "安全库存", "Warningvalue": "预警仓库", "Maxvalue": "最高库存", "Floor": "楼层"}, "MATERIAL_AGVCK": {"WarehouseCode": "仓库编号", "WarehouseName": "仓库名称", "BelongAreaCode": "库区编号", "BelongAreaName": "库区名称", "MaterialAreaTypeName": "库区类型", "PositionCode": "库位编号", "PositionName": "库位名称", "PositionType": "库位类型", "WarehouseLevel": "属性", "Description": "描述"}, "MATERIAL_CKGXJM": {"Floor": "楼层", "Linename": "产线", "WarehouseCode": "MES仓库编号", "WarehouseName": "MES仓库名称", "AGVWarehouseCode": "AGV仓库编号", "AGVWarehouseName": "AGV仓库名称", "BelongAreaCode": "AGV仓库库区编号", "BelongAreaName": "AGV仓库库区名称"}, "MATERIAL_CXCKJM": {"AreaCode": "产线", "LineCode": "工段", "ProductCode": "目标料号", "ProductWarehouseCode": "原材料料仓", "StagingWarehouseCode": "原材料暂存料仓", "MesWarehouseCode": "MES产出虚拟仓编码", "MesWarehouse": "MES产出虚拟仓", "AGVBelongAreaName": "AGV产出下料库区", "AGVBelongAreaCode": "AGV下料库区编码"}, "MATERIAL_CXSLD": {"LineCode": "产线", "EquipmentCode": "工站", "StagingWarehouseCode": "原材料暂存料仓", "MesWarehouseCode": "MES产出虚拟仓编码", "AgvAreaName": "AGV上料库区", "AGVBelongAreaCode": "AGV下料库区编码"}, "MATERIAL_LLZCD": {"ReceivedWarehouseName": "WMS发料仓库", "WarehousePositionName": "WMS领料仓库", "WarehousePositionCode": "WMS领料仓库编码", "Orderid": "WMS订单号", "SN": "转储单流水", "DemandDate": "WMS需求日期", "ReceivedDate": "WMS发料日期", "OrderType": "WMS订单类型", "WavesId": "WMS批次号", "ReserveId": "WMS预留号码", "ReceivedWarehouseCode": "WMS发料仓库编码", "MaterialCode": "物料", "MaterialDescribe": "物料描述", "Batchcode": "领用批次", "Plan_Num": "配送数量", "details": "查看明细", "Unit": "单位"}, "MATERIAL_YLKCGL": {"StockBack": "退料数量", "ReturnNum": "退料数量", "ReturnReason": "退料原因", "Warehousename": "仓库", "SapStore": "SAP库位", "Materialcode": "物料号", "Materialname": "物料名称", "Num": "数量", "Unit": "单位", "Batchcode": "批次", "Backbatchcode": "追溯批次", "Productionline": "产线", "PrepareTeam": "班组", "IsNomarlV": "是否异常", "Productiontime": "生产日期", "Sapcode": "SAP库位", "OldNum": "原有数量", "NewNum": "现有数量", "Description": "原因", "WareHouseName": "仓库", "SapHouse": "SAP库位", "sapcode": "SAP库位", "ReceivedDate": "日期", "MaterialCode": "物料号", "MaterialName": "物料名称", "FullLineName": "产品线", "CompanyName": "工段", "CompanyCode": "工段编码", "MaterialDescription": "物料描述", "Material": "物料", "Quantity": "数量", "Uom": "单位"}, "SHIFT_RYSJ_EYZSJ": {"Index": "序号", "Code": "员工号", "Name": "姓名", "UserAvatar": "员工头像", "Region": "人事子范围", "StaffGroup": "员工子组", "Factory": "厂区", "BusinessDesc": "人事子范围", "BusinessDesc1": "业务类型描述", "WorkshopDesc1": "车间描述", "WorkshopDesc": "厂区", "VirtualOrganization": "虚拟组织", "LeaderName": "上级领导", "LeaderCode": "上级领导工号", "Uwb": "UWB标签号", "Rank": "职位", "Price": "维修成本单价(小时)", "StartValidTime": "单价有效起始时间", "EndValidTime": "单价有效截止时间", "JobTitle": "职称", "StarLevel": "星级", "Feishu": "飞书", "actions": "操作"}, "DFM_DWZH": {"MaterialCode": "物料", "FormUnitName": "源单位名称", "ConvertFormQty": "源单位数量", "ToUnitName": "转换单位名称", "ConvertToQty": "转换单位数量", "EffectiveBeginDate": "生效开始时间", "EffectiveEndDate": "生效结束时间", "Remark": "备注"}, "SHIFT_RYSJ_RYCQ": {"Index": "序号", "Date": "日期", "Segment": "工段", "StaffCode": "员工号", "StaffName": "姓名", "Shift": "班次", "Team": "班组", "ClockInTime": "上班打卡", "ClockOffTime": "下班打卡", "HrTimes": "打卡时长", "WorkTimes": "UWB在岗工时", "UwbWorkTimes": "UWB考勤工时", "Yield": "产量", "PieceworkPost": "计件岗位", "PieceworkWage": "计件工资", "Bonus": "超产奖励", "Source": "借调来源", "Line": "产线", "Type": "员工分类", "Type2": "类型", "Type3": "类别", "State": "状态", "Process": "工序", "StateDisplayName": "出勤", "ConfirmTimes": "确认工时", "actions1": ""}, "TPM_SBGL_SBFLGL": {"classname": "分类名称", "action": "操作", "comment": "备注", "classlevel": "分类层级", "classcode": "分类编号"}, "SHIFT_RYSJ_RYPB": {"SHIFT_RYSJ_RYCQ": ""}, "SHIFT_RYSJ_JJBZ": {"Index": "序号", "MaterielCode": "物料号", "MaterielDescription": "物料描述", "SubsectionOne": "工段", "ShiftSystem": "班制", "SubsectionTwo": "工序", "JobContent": "岗位名称", "Department": "部门分类", "JobTitle": "岗位职称", "StageTarget": "阶段目标（pcs/班/机）", "ManMachineRatio": "人机比", "BasicOutput": "计件基准产能", "TargetPpm": "目标PPM（生产&工艺制定，质量复核）", "BasicWage": "基准工资", "BasicPrice": "基准单价（基准工资/目标产出）", "AllowanceDay": "日津贴标准", "actions": "操作"}, "SHIFT_RYSJ_JJBZ_CCBZ": {"Index": "序号", "MinPercent": "最小百分比", "MaxPercent": "最大百分比", "AllowancePercent": "津贴占比%", "actions": "操作"}, "SHIFT_RYSJ_RYCXPZ": {"LeaderName": "上级领导", "Index": "序号", "StaffCode": "员工号", "StaffName": "姓名", "Line": "产线", "Segment": "工段", "Process": "工序", "MaterialCode": "物料号", "PricePost": "计件岗位", "AndonPost": "安灯岗位", "WokrHours": "班制", "Type": "员工分类", "Type2": "类型", "Type3": "类别", "ChangeShifts": "是否转班", "actions": "操作"}, "TPM_SBGL_SBGLZY": {"RepairCode": "维修工单", "DeviceName": "设备名称", "ProductlineName": "产线名称", "actions": "操作"}, "TPM_SBGL_SBTZGL": {"LineCode": "产线代码", "Index": "序号", "Name": "设备名称", "EquipListCode": "编号", "SubName": "设备别名", "Factory": "工厂", "Area": "区域", "Group": "分组", "Accountingmethod": "入账方式", "OldCode": "旧编码", "serialnumber": "机器序列号", "Licenseplatenumber": "牌照号码", "Factorycode": "出厂编码", "EquipItem": "设备位号", "EquipType": "设备分类", "EquipGroup": "设备类别", "brand": "品牌", "Specmodel": "规格型号", "Number": "数量", "Unit": "单位", "EquipStatus": "设备状态", "UseStatus": "使用状态", "UseStatusTag": "设备状态Tag", "IncomeDate": "入账日期", "isImported": "是否进口", "manufacturer": "制造商", "supplier": "供应商", "producer": "产地", "Manufacturingdate": "制造日期", "useYear": "使用年限", "dateproduction": "出厂日期", "factoryDate": "进厂日期", "TestDate": "调试日期", "UseDate": "使用日期", "EquipValue": "设备原值", "EquipSize": "设备尺寸", "EquipWeight": "设备重量", "FixLimit": "保修期限", "Storagelocation": "存放地点", "Insidelocation": "内部存放地点", "position": "存放位置", "Nowposition": "现存放地点", "Inventorysituation": "盘点情况", "Keyequipmentstatistics": "关键设备统计", "personresponsible": "责任人", "equipmentSupervisor": "设备主管", "Inspectiontime": "点检时间", "Firstmaintenancetime": "首次保养时间", "LastFirstmaintenancetime": "上次保养时间", "Expectedscrapdate": "预计报废日期", "Scrapdate": "报废日期", "Assetnumber": "资产编号", "AssetName": "资产名称", "WorkCenter": "工作中心", "CostCenter": "成本中心", "Department": "所属部门", "SuperiorEquit": "上级设备", "Equipnature": "设备性质", "EquipCode": "设备编号", "Code": "EAM编码", "Type": "设备类型", "EamCode": "EAM编码", "EipCode": "EIP编码", "LeaveCode": "出厂编码", "MyProject": "保养项目", "Eqmodel": "型号", "BarCode": "条形码", "MyCycle": "保养周期", "PersonName": "责任人", "Purchasedate": "制造日期", "FirstMaintenanceTime": "首次保养时间", "LastMaintenanceTime": "上次保养时间", "Manufacturer": "制造商", "Contacts": "联系人", "Tel": "电话", "MaintainPosition": "定期保养部位", "MaintainMethod": "保养方法", "MachineHour": "保养时长(分钟)", "MaintainPlandata": "计划维修保养时间", "ProductlineName": "产线名称", "LineName": "工段名称", "SegmentName": "工站名称", "Productiondate": "出厂日期", "Age": "使用年限", "Remark": "备注", "Jigspec": "备件规格", "Status": "状态", "Cost": "成本", "Smq": "扫码枪", "Dyj": "打印机", "Comment": "备注", "Pmj": "喷码机", "SBQD": "设备清单", "actions": "操作"}, "TPM_SBGL_SBTZGL_BPBJQD": {"Remark": "备注", "Index": "序号", "Name": "备件名称", "Jigspec": "备件规格", "Code": "备件编码", "Jigtype": "备件类型", "Qty": "申请数量", "Maxqty": "最大库存数量", "Safeqty": "安全库存数量", "SAPQty": "SAP库存数量", "brand": "品牌", "supplier": "供应商", "OutStock": "出库仓库", "Batch": "批次号", "OutWarehouse": "出库库位", "Unit": "单位", "actions": "操作"}, "TPM_SBGL_SBTZGL_SBWJ": {"Code": "编号", "Name": "名称", "GroupName": "分组", "DataType": "类型", "DefaultValue": "默认值", "Remark": "备注", "Index": "序号", "FileName": "文件名称", "FileVersion": "版本", "Type": "类型", "Size": "文件大小", "FileUpload": "文件上传", "CreaterTime": "上传时间", "actions": "操作"}, "TPM_SBGL_SBTZGL_SBZB": {"Index": "序号", "RunningBeat": "指标名称", "BeatSpeeding": "指标值", "MachineHour": "阈值", "actions": "操作"}, "TPM_SBGL_SBTZGL_SBBOM": {"Index": "序号", "ParentId": "请选择父级", "AccessoriesName": "名称", "AccessoriesCode": "编码", "Type": "类型", "Specifications": "规格型号", "Remark": "备注", "Quantity": "数量", "actions": "操作"}, "TPM_SBGL_SBTZGL_SBLL": {"WXDH": "维修单号", "BXNL": "报修内容", "ZT": "状态", "JJD": "紧急度", "WXZT": "维修状态", "SFTJ": "是否停机", "GDLX": "工单类型", "GDLY": "工单来源", "GLDH": "关联单号", "SparePartsCode": "备件编号", "SparePartsName": "备件名称", "status": "状态", "requestNo": "申请单号", "SType": "备件类型", "SparePartsSpec": "规格型号", "Batch": "批次号", "supplier": "供应商", "number": "数量", "BYZT": "保养状态", "BYJHSJ": "保养计划时间", "BYWCSJ": "保养完成时间", "ZRR": "责任人", "BYJG": "保养结果", "DJZT": "点检状态", "DJJHSJ": "点检计划时间", "DJWCSJ": "点检完成时间", "DJR": "点检人", "DJJG": "点检结果", "SBZG": "设备主管", "Index": "序号", "Action": "动作", "Behavior": "行为", "Creator": "操作人", "CreateDate": "操作时间", "Remark": "备注"}, "TPM_SBGL_SBBJGL": {"Index": "序号", "Remark": "货架编号", "SparePartsCode": "备件编号", "SType": "备件类型", "SparePartsSpec": "规格型号", "Batch": "批", "BookDate": "入库时间", "Stock": "仓库", "Warehouse": "库位", "brand": "品牌", "supplier": "供应商", "SparePartsName": "备件名称", "UnitPrice": "单价", "CurrentStock": "当前库存", "MaxStock": "最大库存", "MinStock": "最小库存", "actions": "操作", "Cycle": "采购周期", "ExtendTime": "建议下次采购日期", "Introduce": "备注", "_KCDY": "库存大于", "_KCXY": "库存小于", "TBKC": "同步库存", "isselector": "当前库存小于最小库存"}, "TPM_SBGL_SBBJGL_XQ": {"Index": "序号", "Buynum": "状态", "Usednum": "数量", "Owner": "使用人", "actions": "操作"}, "TPM_SBGL_SBBJGL_RK": {"Index": "序号", "Remark": "货架编号", "SparepartCode": "物料编号", "SparepartName": "物料名称", "Buynum": "入库数量"}, "TPM_SBGL_SBBJGL_CK": {"Index": "序号", "DeviceCode": "EAM编号", "DeviceName": "设备名称", "PartsCode1": "物料编号", "Parts1": "物料名称", "Parts1Num": "出库数量"}, "TPM_SBGL_SBBJGL_CRK": {"Index": "序号", "Buynum": "类型", "Remark": "货架编号", "SparepartCode": "备件编号", "SparepartName": "备件名称", "Usednum": "数量", "CreateDate": "时间"}, "TPM_SBGL_BJZSJ": {"Subject": "备件科目", "PurchaseCycle": "采购周期", "MinPurchaseQty": "最低采购数", "Remark": "备注", "SparePartsCode": "备件编号", "SparePartsName": "备件名称", "SType": "备件类型", "SparePartsSpec": "规格型号", "Min": "安全库存下限", "Max": "安全库存上限", "Useyears": "使用寿命", "Unit": "单位"}, "TPM_SBGL_BJKZGL": {"Unit": "单位", "SparePartsCode": "备件编号", "SparePartsName": "备件名称", "SType": "备件类型", "SparePartsSpec": "规格型号", "BatchCode": "批次号", "InstoreDate": "入库时间", "Warehouse": "仓库", "StorageBin": "库位", "Stock": "库存", "Brand": "品牌", "Supplier": "供应商"}, "TPM_SBGL_SBBJGL_CKSZ": {"Index": "序号", "Code": "备件编码", "Name": "备件名称", "Type": "备件类型", "Spec": "规格型号", "Min": "安全库存下限", "Max": "安全库存上限", "Useyears": "使用寿命", "Procycle": "采购周期(天)", "Unit": "单位", "Remark": "备注", "actions": "操作"}, "TPM_SBGL_SBWXGD": {"Index": "序号", "FaultCode": "故障类型", "_JYXM": "检验项目", "_SFHG": "是否合格", "_BHGLY": "详述不合格理由", "_FWDH": "服务单号", "_SQR": "申请人", "_XQYY": "需求原因", "_FWNR": "服务内容", "_FWZT": "服务状态", "_KXC": "是否需要看现场", "_REQ": "REQ号", "_SPYJ": "审批意见", "_PR": "PR号", "_PO": "工单", "_FWRQ": "服务日期", "_SFJS": "该机械/工程是否接受", "_YSYJ": "验收意见", "_YJQR": "用家确认(副主管级以上)", "_BMLDQP": "部门领导签批", "_YSBH": "验收编号", "_YSRQ": "验收日期", "_BJBH": "备件编号", "_BJMC": "备件名称", "_BJLX": "备件类型", "_GGXH": "规格型号", "_PCH": "批次号", "_GYS": "供应商", "_SL": "数量", "_DW": "单位", "_GDLY": "工单来源", "_ZPR": "维修主管", "_JHKS": "计划开始时间", "_JHJS": "计划结束时间", "_CLKS": "开始处理时间", "_CLJS": "结束处理时间", "_WXSC": "维修时长(h)", "_WXZT": "维修状态", "_WXGC": "维修过程描述", "_YYFX": "原因分析", "_YYFXJL": "原因分析结论", "_XTFL": "系统分类", "_YYFL": "原因分类", "_WXPJ": "维修评价", "_GDLX": "工单类型", "_BXLR": "保修内容", "_ZT": "状态", "_JJD": "紧急度", "isStop": "是否停机", "_GLDH": "关联单号", "_CBZX": "成本中心", "_ZDWCRQ": "指定完成日期", "_DZZG": "生产主管", "_JDR": "接单人", "_BXR": "报修人", "_CX": "产线", "_BXSJ": "报修时间", "_QRR": "确认人", "_QRSJ": "确认时间", "_PJ": "评价", "EventNum": "事件号", "RepairCode": "维修工单号", "RepairStatus": "维修状态", "RepairType": "维修类型", "InputType": "录入类型", "EipCode": "EIP编码", "LeaveCode": "出厂编码", "DeviceCode": "设备编号", "Eqmodel": "型号", "Purchasedate": "制造日期", "DeviceName": "设备名称", "ProductlineCode": "产线编码", "ProductlineName": "产线名称", "LineCode": "工段编码", "LineName": "工段名称", "ShiftName": "班次", "BomName": "BOM", "Allday": "是否全班次", "AbnormalDesc": "异常描述", "PlanWorkuser": "指定执行人员", "CompleteWorkuser": "实际执行人", "PlanManhours": "计划工时", "PriceSum": "成本", "WorkSum": "工时", "Comstardate": "维修开始时间", "Completedate": "维修完成时间", "Remark": "备注", "actions": "操作"}, "TPM_SBGL_SBWXJL_WXMX": {"Index": "序号", "ExceptionDesc": "异常描述", "RepairProcess": "维修过程描述", "CurrentSituation": "原因分析—现状", "RepairStatus": "维修状态", "Reasons1": "原因分析", "Parts1": "消耗备件", "Parts1Num": "消耗备件数量", "RepairUser": "承修人", "StartTime": "开始时间", "EndTime": "结束时间", "RepairHours": "维修时长(小时)", "RepairPrice": "人工成本", "FaultPhenomenon": "故障现象", "IsCase": "是否作为案例", "actions": "操作"}, "TPM_SBGL_SBWXJL_TSMX": {"Index": "序号", "Debugtime": "报修时间", "Debugperson": "报修人姓名", "Dealtime": "调试开始时间", "Fixedtime": "调试结束时间", "Maintenanceperson": "处理人姓名", "Confirmtime": "确认时间", "Confirmperson": "确认人姓名", "Debugtype": "调试类型", "Debugcode": "调试代码", "Failuredescription": "调试描述", "Remark": "备注", "Status": "状态", "Tools": "使用工具", "actions": "操作"}, "TPM_SBGL_SBWXJL_WXCB": {"Index": "序号", "PartsRepairPrice": "备件成本", "RepairPrice": "人工成本"}, "TPM_SBGL_SBBYXM": {"Status": "保养状态", "Index": "序号", "DeviceCategory": "设备类型", "McProject": "保养项目", "CheckStandard": "检验标准", "Methods": "措施方法", "Tools": "工具", "ProjectCategory": "项目分类", "UsingTime": "保养用时", "PersonName": "责任人", "UpperLimit": "上限", "LowerLimit": "下限", "InputType": "输入方式", "DataFrom": "数据来源", "MaintainCycle": "保养周期", "IsStop": "是否停机", "Duration": "保养时长(分钟)", "Isenable": "状态", "Classify": "分类", "Remark": "备注", "actions": "操作", "ProjectCode": "项目编号", "ProjectName": "项目名称", "Inscycle": "保养周期", "Insfrequency": "保养频次", "byzq": "保养周期", "bypl": "保养频率", "executiontime": "执行时间", "Inspectiontime": "点检时间", "File": "文件"}, "TPM_SBGL_WXJY": {"DeviceCategoryName": "设备分类名称", "DeviceCategoryId": "设备分类编号", "Source": "来源", "Type": "类型", "Description": "问题描述", "IsStop": "是否停机", "Urgency": "紧急度", "RepairDuration": "维修时长（min）", "FaultCategory": "系统分类", "ReasonCategory": "原因分类", "RepairRecordDesc": "维修过程描述", "Reason": "原因分析", "ReasonResult": "原因分析结论", "RepairNature": "维修性质", "QueryCount": "查询次数", "Keyword": "关键字", "Remark": "备注", "action": "操作"}, "TPM_SBGL_SBDJXM": {"Context": "备注", "ProjectName": "项目名称", "Index": "序号", "DeviceCategory": "设备类型", "McProject": "点检项目", "CheckStandard": "检验标准", "Methods": "措施方法", "Tools": "工具", "ProjectCategory": "项目分类", "PersonName": "责任人", "UpperLimit": "上限", "LowerLimit": "下限", "InputType": "输入方式", "DataFrom": "数据来源", "MaintainCycle": "点检周期", "Duration": "点检时长(分钟)", "Isenable": "状态", "Classify": "分类", "Remark": "备注", "actions": "操作", "ProjectCode": "项目编号", "Inscycle": "点检周期", "Insfrequency": "点检频次", "executiontime": "执行时间", "Inspectiontime": "点检用时", "File": "文件"}, "TPM_SBGL_SBBYJH": {"Wo": "维修工单号", "Index": "序号", "ProductlineCode": "产线编码", "ProductlineName": "产线名称", "LineCode": "工段编码", "LineName": "工段名称", "MaintainProjectCode": "保养项目代码", "MaintainProjectName": "保养项目名称", "Type": "分类", "MaintenancePeriod": "保养周期", "MaintenanceTime": "保养频率", "isStop": "是否停机", "CheckStandard": "检验标准", "Methods": "措施方法", "Duration": "保养用时", "DeviceId": "设备编码", "DeviceCode": "设备名称", "DeviceCategory": "设备类型", "MaintainUsername": "保养负责人", "PersonName": "保养负责人", "PlanTimeTask": "计划保养日期", "StartTime": "计划开始时间", "EndTime": "计划结束时间", "ActualStartTime": "实际开始时间", "ActualFinishTime": "实际完成时间", "Executor": "执行人", "Status": "保养状态", "MaintainUser": "保养人", "Remark": "备注", "PropertyCode": "维护类型", "actions": "操作", "FinishMaintainDate": "上次保养时间", "PlanMaintainDate": "计划保养时间", "UsingTime": "保养用时", "Area": "区域", "Groups": "分组", "MaintainBy": "责任人", "Manager": "设备主管"}, "TPM_SBGL_SBBYJH_XQ": {"Context": "保养结果", "MaintainBy": "保养人", "Status": "保养状态", "Index": "序号", "byzq": "保养周期", "bypl": "保养频率", "IsStop": "是否停机", "UsingTime": "保养用时", "MaintainProject": "保养项目", "MaintainValue": "保养值", "MaintainStatus": "保养状态", "MaintainUser1": "保养人", "MaintainDate": "开始时间", "Classify": "分类", "PlanMaintainDate": "计划保养时间", "ActualFinishTime": "实际完成时间", "InputType": "输入方式", "McProject": "点检项目", "McValue": "点检值", "McStatus": "点检状态", "FjStatus": "复检状态", "FjValue": "复检记录", "FjExecutor": "复检人", "McUser": "点检人", "McDate": "点检时间", "ProjectCode": "项目编号", "ProjectName": "项目名称", "CheckStandard": "检验标准", "Methods": "措施方法", "Tools": "工具", "CheckCycle": "点检周期", "Executor": "实际执行人", "Remark": "备注", "SparePartsCode": "备件编号", "SparePartsName": "备件名称", "SType": "备件类型", "SparePartsSpec": "规格型号", "Batch": "批次号", "supplier": "供应商", "number": "数量", "status": "状态", "requestNo": "申请单号", "actions": "操作"}, "TPM_SBGL_SBBYGZ": {"Index": "序号", "MaintainProject": "设备类型", "MaintainCycle": "保养周期", "MaintenancePeriod": "保养周期(天)", "Remark": "修改原因", "Isenable": "状态", "Startime": "开始时间", "Endtime": "结束时间", "Duration": "保养时长(分钟)", "actions": "操作"}, "TPM_SBGL_SBDJJH": {"_DJXMQD": "点检项目清单", "Index": "序号", "DeviceName": "设备名称", "DeviceCode": "设备编号", "DeviceId": "EAM编码", "DeviceCategory": "设备类型", "Shift": "班次", "CheckPlanTime": "计划点检时间", "ActualFinishTime": "点检完成时间", "McStatus": "点检状态", "FileCode": "文件编号", "Remark": "备注", "actions": "操作", "factory": "工厂", "area": "区域", "group": "分组", "PersonName": "责任人", "equipmentSupervisor": "设备主管"}, "TPM_SBGL_SBDJJH_XQ": {"Context": "点检结果", "Index": "序号", "CheckFile": "点检照片", "FilePath": "文件", "McProject": "点检项目", "McValue": "点检值", "Executor": "实际执行人", "ImplementTime": "执行时间", "LowerLimit": "下限", "UpperLimit": "上限", "Status": "状态", "Cost": "点检用时", "File": "文件", "PlanCheckDate": "计划点检时间", "ActualFinishTime": "实际完成时间", "InputType": "输入方式", "McStatus": "点检状态", "FjStatus": "复检状态", "FjValue": "复检记录", "FjExecutor": "复检人", "McUser": "点检人", "McDate": "实际点检时间", "ProjectCode": "项目编号", "Classify": "分类", "CheckStandard": "检验标准", "Methods": "措施方法", "Tools": "工具", "CheckCycle": "点检周期", "CheckTime": "点检频次", "Remark": "备注", "actions": "操作", "Inspectiontime": "实际点检时间"}, "TPM_SBGL_SBDJGZ": {"Index": "序号", "McProject": "点检项目", "McCycle": "点检周期", "ImplementTime": "执行时间", "StartUsing": "是否启用", "Remark": "备注", "actions": "操作"}, "TPM_SBGL_BJFFGL": {"_bjkm": "备件科目", "_ckdh": "出库单号", "_gldh": "关联单号", "_zt": "状态", "_bjbm": "备件编码", "_bjmc": "备件名称", "_ggxh": "规格型号", "_bjlx": "备件类型", "_thsl": "退回数量", "_pch": "批次号", "_ckck": "出库仓库", "_ckkw": "出库库位", "_cksl": "申请数量", "_cklx": "出库类型", "_slr": "申领人", "_slsj": "申领时间", "_glry": "管理人员", "_cksj": "出库时间", "_cgsksj": "采购开始时间", "_cgjssj": "采购结束时间", "action": "操作"}, "TPM_SBGL_BJYJCX": {"Remark": "备注", "_bjbm": "备件编码", "_bjmc": "备件名称", "_bjlx": "备件类型", "_pp": "品牌", "_ggxh": "规格型号", "_kc": "库存", "_dw": "单位", "_kcaqsx": "库存安全上限", "_kcaqxx": "库存安全下限", "_cgzq": "采购周期(天)", "_zdcgsl": "最低采购数量", "_yjsj": "预警时间"}, "TPM_SBGL_WDGD": {"LineCode": "产线名称", "RecodeStatus": "维修记录状态", "gzxx": "故障现象", "wxdh": "维修单号", "sbh": "设备号", "sbmc": "设备名称", "bxlr": "报修内容", "zt": "工单状态", "jjd": "紧急度", "jhkssj": "计划开始时间", "jhwcsj": "计划完成时间", "wxzt": "维修状态", "gzxz": "故障性质", "sftj": "是否停机", "action": "操作", "gdlx": "工单类型", "gdly": "工单来源", "gldh": "关联单号", "zdwcrq": "指定完成日期", "dbzg": "生产主管", "bxr": "报修人", "bxsj": "报修时间", "zpr": "维修主管", "jdr": "接单人", "kscl": "开始处理时间", "ksjs": "结束处理时间", "wxsc": "维修时长（h）", "xtfl": "系统分类", "yyfl": "原因分类", "wxgcms": "维修过程描述", "yyfx": "原因分析", "yyfxjl": "原因分析结论", "wxxz": "维修性质", "gzbw": "故障部位", "qrr": "确认人"}, "TPM_SBGL_JZTXCX": {"AccountAssetNo": "有账资产码", "bmnbbh": "部门内部编号", "jlqjmc": "计量器具名称", "gg": "规格", "jdff": "检定方法", "tjrq": "调校日期", "yxq": "有效期", "jdbm": "检定部门", "sybm": "使用部门", "fbm": "分部门", "fzdd": "放置地点", "abcfl": "ABC分类"}, "TPM_SBGL_JLRWGL": {"cjr": "创建人", "jyjg": "检验结果", "jdff": "检定方法", "abcfl": "ABC分类", "lb": "类别", "yxqks": "有效期开始", "yxqjs": "有效期结束", "zzwcrq": "最终完成日期", "jyzrr": "检验责任人", "zt": "状态", "actions": "操作", "bh": "编号", "sbmc": "设备名称", "sl": "数量", "ksrq": "开始日期", "jsrq": "结束日期", "gg": "规格", "jyzt": "检验状态", "jyrq": "检验日期", "yxq": "有效期", "hgzh": "合格证号", "file": "文件", "jyzq": "检验周期(月)", "jdbm": "检定部门", "sybm": "使用部门", "fbm": "分部门", "fzdd": "放置地点"}, "TPM_SBGL_JLQJGL": {"MeasureNo": "部门内部编号", "AccountAssetNo": "有账资产码", "bh": "编号", "sbmc": "设备名称", "xh": "型号", "gg": "规格", "zt": "状态", "jyzt": "检验状态", "abcfl": "ABC分类", "lb": "类别", "yxq": "有效期", "clfw": "测量范围", "dj": "等级", "jd": "精度", "dsjfdz": "d实际分度值", "ejdfdz": "e检定分度值", "kxs": "K系数", "yc": "允差", "zzcjmc": "制造厂家名称", "ccrq": "出厂日期", "ccbh": "出厂编号", "jbh": "旧编号", "sqjdrq": "申请检点日期", "jyzq": "检验周期(月)", "jdff": "检定方法", "jyrq": "检验日期", "jdbm": "检定部门", "sybm": "使用部门", "fbm": "分部门", "fzdd": "放置地点", "qjglry": "器具管理人员", "yzzcm": "有账资产码", "wzzcm": "无账资产码", "cxm": "磁吸码", "bc": "备注", "actions": "操作", "jyr": "检验人", "jysj": "检验时间", "hgzh": "合格证号", "file": "文件"}, "TPM_SBGL_SBDXJH": {"dxgdh": "大修工单号", "jhks": "计划开始日期", "jhjs": "计划结束日期", "sjks": "实际开始日期", "sjjs": "实际结束日期", "wxgdbh": "维修工单编号", "sbh": "设备号", "sbmc": "设备名称", "bxnr": "报修内容", "jjd": "紧急度", "qwks": "期望开始时间", "qwwc": "期望完成时间", "dzzg": "生产主管", "bxr": "报修人", "bxsj": "报修时间", "wxzg": "维修主管", "jdsj": "接单时间", "fzr": "负责人", "nd": "年度", "yf": "月份", "cx": "产线", "dxnr": "大修内容", "rwms": "任务描述", "rwrn": "任务内容", "jhksrq": "计划开始日期", "jhjsrq": "计划结束日期", "zb": "周别", "zt": "状态", "comment": "备注", "ys": "用时", "pj": "评价", "pjr": "评价人", "pjsj": "评价时间", "pjbz": "评价备注", "action": "操作"}, "TPM_SBGL_FWDGL": {"Detail": "详情", "Requestcontent": "服务内容", "PurchasePoItem": "POItem号", "PurchasePoLine": "服务单行号", "ysyj": "验收意见", "ysjg": "验收结果", "yssj": "验收时间", "shsj": "送货时间", "sfjs": "该机械/工程是否接受", "yjqs": "用家确认", "bmsp": "部门领导签批", "prh": "PR号", "poh": "PO号", "gys": "供应商", "reqh": "REQ号", "spyj": "审批意见", "spr": "审批人", "spsj": "审批时间", "spjg": "审批结果", "sbh": "设备号", "wxdh": "维修工单号", "fwdh": "服务单号", "fwdzt": "服务单状态", "sqr": "申请人", "sqsj": "申请时间", "xqyy": "需求原因", "fwnr": "服务内容", "xykxc": "需要看现场", "action": "操作"}, "TPM_SBGL_WDBX": {"LineCode": "产线代码", "ConfirmResult": "维修分数", "ConfirmComment": "确认备注", "jdsj": "接单时间", "qrsj": "确认时间", "gdbh": "工单编号", "sbh": "设备号", "sbmc": "设备名称", "gdly": "工单来源", "gdlx": "工单类型", "bxlr": "报修内容", "zt": "状态", "wjmc": "文件名称", "sftj": "是否停机", "jjd": "紧急度", "bxsb": "报修设备", "gzxx": "故障现象", "gldh": "关联单号", "bxsj": "报修时间", "qwkssj": "期望开始时间", "qwwcsj": "期望完成时间", "bxr": "报修人", "dbzg": "生产主管", "wxzg": "维修主管", "Remark": "备注", "sctp/sp": "上传图片/视频", "action": "操作"}, "TPM_SBGL_GZZSK": {"Index": "序号", "RepairProject": "案例名称", "CheckStandard": "检验标准", "PersonName": "问题原因", "Methods": "措施方法", "Tools": "工具", "Remark": "备注", "actions": "操作"}, "TPM_SBTJJL": {"Line": "产线", "Segment": "工段", "Unit": "工站", "MachineCode": "设备", "Shift": "班组", "Reason": "停机原因", "Difftime": "停机时长(分钟)", "LossQty": "损失量", "StartTime": "开始时间", "EndTime": "结束时间", "actions1": ""}, "TPM_SBTJJL_XQ": {"Line": "产线", "Segment": "工段", "Unit": "工站", "MachineCode": "设备", "StopReason": "停机原因", "Duration": "时长(分钟)", "StartTime": "开始时间", "EndTime": "结束时间", "LossNum": "损失量", "ReworkNum": "返工量", "Remark": "备注", "actions1": ""}, "MATERIAL_YLFXDDY": {"LineID": "产品线", "ProductionName": "成品描述", "MaterialCode": "零件物料号", "MaterialName": "零件物料描述", "Unit": "单位", "PlanDate": "需求日期", "Shift": "白班班次", "Team": "白班班组", "DemandNum": "需求数量", "ActualNum": "实发数量", "PlanDateB": "需求日期", "ShiftB": "夜班班次", "TeamB": "夜班班组", "DemandNumB": "需求数量", "ActualNumB": "实发数量"}, "MATERIAL_WIPKCGL": {"AreaCode": "产品线", "WarehouseName": "MES虚拟库", "AgvwarehouseName": "AGV仓库", "AgvareawarehouseName": "AGV库区", "AgvplacewarehouseName": "AGV库位", "Materialcode": "物料编码", "Materialname": "物料名称", "Outputdate": "产出时间", "Batchnum": "批次数量", "Anum": "当前数量(A)", "Bnum": "缺陷数量(B)", "Cnum": "废品数量(C)", "Status": "状态", "Batchcode": "批次号"}, "MATERIAL_JSGL": {"QualityGuaranteePeriod": "保质期(小时)", "QualityConfirmTime": "大瓶胶水提醒时间(小时)", "QualityNoticeTime": "小瓶胶水预警时间(分钟)", "GlueDate": "注胶时间", "IsLoading": "上下料状态", "LoadingDate": "上料时间", "UnLoadingDate": "下料时间", "EquipmentCode": "设备编码", "SingleTubeWeight": "单管容量(克)", "Materialname": "胶水类型", "Serialno": "胶管条码", "Expdate": "失效时间", "WarehouseCode": "仓库编号", "WarehouseName": "仓库名称", "Materialbatchno": "物料批次", "Materialcode": "物料编号", "Indate": "来料时间", "Shelflife": "保质期限", "glueCode": "胶水型号", "warehouse": "仓库", "num": "胶管数量"}, "ariaLabel": {"sortDescending": "：降序排列。", "sortAscending": "：升序排列。", "sortNone": "：未排序。", "activateNone": "点击以移除排序。", "activateDescending": "点击以降序排列。", "activateAscending": "点击以升序排列。"}, "INV_LQJL": {"ModifyDate": "创建时间", "Batchno": "批号", "LotNo": "批次", "detail": "详情", "BagWeight": "单包重量", "EquipmentName": "设备名称", "Material": "物料", "Type": "类型", "Index": "序号", "BagSize": "规格", "Comment": "评论", "Quantity": "数量", "InventoryQuantity": "库存数量", "NeedQuantity": "需求数量", "Completion": "完成", "Way": "方式", "Status": "状态", "StartDate": "开始时间", "ComingDate": "到货时间", "TrayNumber": "托数", "UserNumber": "当天用量", "ActualQuantity": "实际到货量", "Inventqty": "库存数量", "Complete": "% 完成度", "Requesttype": "方式", "CreateDate": "创建时间", "PlannedTime": "到货时间", "ArraveTime": "到货时间", "Remark": "评论"}, "INV_KCQD": {"Sapformula": "配方号", "Bucketnum": "桶号", "Suppiername": "供应商", "Formula": "配方号", "detail": "详情", "Material": "物料", "PpmPro": "工单", "Class": "类型", "BatchStatus": "批次状态", "Batch": "批次", "Remark": "备注", "SSCCStatus": "追溯码状态", "SSCC/Container": "追溯码/容器", "Quantity": "数量", "Location": "位置", "Expiration": "有效期", "Created": "创建时间", "UOM": "单位", "Lot": "批次", "Result": "结果"}, "INV_SCLS": {"SendTime": "发送时间", "Formula": "配方号", "Mblnr": "sap凭证号", "Msg": "回传失败原因", "Type": "sap结果", "ProcessOrder": "工单", "Machine": "机器", "Destination": "目的地", "ShiftId": "班次", "Source": "来源", "SAP": "SAP", "Reason": "原因", "Comment": "评论", "ProductDate": "产出时间", "Date": "日期", "Material": "物料", "BatchStatus": "批次状态", "Batch": "批次", "SSCCStatus": "追溯码状态", "SendStates": "发送状态", "SSCC": "追溯码", "operate": "操作", "Quantity": "数量"}, "INV_WLKCPD": {"BatchCode": "批次号", "InventQty": "库存数量总数", "MoveActureQty": "本次盘点实数总数", "DifferenceQty": "差异值总数", "Sapno": "Sap单号", "Remark": "备注", "Isread": "已读", "Inventtype": "状态", "Tasktype": "任务类型", "Over": "盘存结束", "MaterialName": "物料名称", "MaterialCode": "物料编码", "Material": "物料", "StatusLot": "", "LotId": "物料批次", "StatusSlot": "", "SubLotId": "物料追溯码", "CurrentQuantity": "库存数量", "Equipment": "库存位置", "Movetime": "盘点日期", "ActualQuantity": "本次盘点实数", "operate": "操作", "Result": "盘点结果", "Difference": "差异值", "Diff": "差异比例", "Reason": "差异原因", "detail": "详情", "Plandate": "计划盘点时间", "TaskStatus": "盘点任务状态", "ModifyDate": "更新时间", "CreateUserId": "操作人"}, "INV_HTKCPD": {"Sapformula": "配方号", "Over": "盘存结束", "MaterialName": "喉头名称", "MaterialCode": "喉头编码", "Material": "喉头料", "StatusLot": "", "LotId": "喉头批次", "StatusSlot": "", "SubLotId": "喉头追溯码", "CurrentQuantity": "库存数量", "Equipment": "库存位置", "Movetime": "盘点日期", "ActualQuantity": "本次盘点实数", "operate": "操作", "Result": "盘点结果", "Difference": "差异值", "Diff": "差异比例", "Reason": "差异原因", "detail": "详情", "Plandate": "计划盘点时间", "TaskStatus": "盘点任务状态", "ModifyDate": "更新时间", "CreateUserId": "操作人"}, "INV_YLKCPD": {"Over": "盘存结束", "MaterialName": "原料名称", "MaterialCode": "原料编码", "Material": "原料", "StatusLot": "", "LotId": "原料批次", "StatusSlot": "", "SubLotId": "原料追溯码", "CurrentQuantity": "库存重量", "Equipment": "库存位置", "Movetime": "盘点日期", "ActualQuantity": "本次盘点实数", "operate": "操作", "Result": "盘点结果", "Difference": "差异值", "Diff": "差异比例", "Reason": "差异原因", "detail": "详情", "Plandate": "计划盘点时间", "TaskStatus": "盘点任务状态", "ModifyDate": "更新时间", "CreateUserId": "操作人"}, "INV_SCTJ": {"Formula": "配方号", "detail": "详情", "ExecutionStatus": "执行状态", "ProcessOrder": "工单", "Machine": "机器", "Material": "物料", "Shift": "班次", "ShiftDate": "班次日期", "Line": "产线", "Planned": "计划", "Daily": "当日", "Total": "总计", "Complete": "% 完成度", "Destination": "目的地", "LastProduction": "上次生产", "Batch": "缸次", "Quantity": "数量", "Inventory": "库存", "SublotCount": "追溯码数量", "operate": "操作"}, "INV_YJC": {"TraceCode": "追溯码", "BatchPallet": "批次托盘", "Loaction": "位置", "ProcessOrder": "工单", "Material": "物料", "Batch": "缸次", "Quantity": "数量", "Deadline": "截止日期", "PreCheck": "预检", "Tipping": "投料"}, "INV_XFHL": {"SendTime": "发送时间", "SSCCValue": "追溯码", "Suppiername": "供应商", "Mblnr": "sap凭证号", "Msg": "回传失败原因", "Type": "sap结果", "SendStates": "发送状态", "ReverseState": "反冲状态", "ProcessOrder": "工单", "Machine": "机器", "Destination": "目的地", "ShiftId": "班次", "Source": "设备来源", "SAP": "SAP", "Reason": "原因", "Comment": "评论", "ProductDate": "产出时间", "Date": "时间", "Material": "物料", "BatchStatus": "批状态", "Batch": "批", "SSCCStatus": "追溯码状态", "SSCC": "追溯码", "operate": "操作", "Quantity": "数量"}, "INV_CSLS": {"Suppiername": "供应商", "TMode": "方式", "OldLocation": "来源", "NewLocation": "源物料", "BatchStatus": "批状态", "Batch": "缸次", "NewLotId": "批次", "OldLotId": "批次", "SSCCStatus": "追溯码状态", "SSCC": "追溯码", "sscc/container1": "追溯码", "sscc/container2": "追溯码", "Destination": "目的地", "DestinationMaterial": "目的地物料", "Quantity": "数量", "SAP": "SAP", "Type": "类型", "Date": "日期", "OldProBatch": "源订单号", "WmsPrintno": "WMS打印单号", "SapPrintno": "SAP单号", "Tranremarks": "备注", "NewProBatch": "目地订单号", "operate": "操作"}, "INV_RQGL": {"detail": "详情", "split": "拆分", "Status": "状态", "Location": "位置", "Material": "物料", "BatchStatus": "批状态", "Batch": "批", "SSCCStatus": "追溯码状态", "SSCC": "追溯码", "id": "ID", "Quantity": "数量", "Expiration": "有效期", "PO": "工单（批）", "StatusTime": "状态时", "SSCC/Container": "追溯码/容器", "Result": "结果", "BinSLOC": "SAP位置", "Allergens": "过敏原", "Action": "操作", "Comment": "评论", "User": "用户", "Date": "日期"}, "INV_TPQD": {"detail": "详情", "Machine": "机器", "ProcessOrder": "工单", "Material": "物料", "Pallet": "批次/追溯码", "Destination": "目的地", "Quantity": "数量", "PrintCount": "打印数量", "InsortedAt": "分类", "Verified": "已确认", "Produced": "产出", "operate": "操作"}, "INV_PCTP": {"Sequence": "序号", "Formula": "配方号", "PlanTime": "计划日期", "detail": "操作", "id": "容器ID", "Loaction": "位置", "Bin": "SAP 位置", "PO": "工单", "Machine": "机器", "Batch": "批", "Material": "物料", "FullBags": "整包", "PartialBags": "部分包", "TotalBags": "包总数", "Complete": "完成", "CreatedBy": "创建自", "Date": "时间", "BatchStatus": "批状态", "SSCCStatus": "追溯码状态", "SSCC": "追溯码", "Type": "类型", "Expiry": "有效期", "User": "用户", "TransferDate": "传输日期", "SourceSSCC": "源追溯码", "Quantity": "数量", "Action": "操作", "Details": "明细"}, "INV_YCLGD": {"Sequence": "序号", "PlanStartDate": "计划日期", "FormulaNo": "配方号", "detail": "详情", "Label": "备料大标签", "Line": "产线", "Po": "工单", "Machine": "机器", "Batch": "缸次", "Material": "物料", "IsOver": "投料前检查是否完成", "Confirmed": "投料前检查确认人", "ConfirmedDate": "确认时间"}, "INV_YCLGDBL": {"Sequence": "序号", "PlanStartTime": "计划时间", "FormulaNo": "配方号", "detail": "明细", "Label": "大标签", "ShiftName": "备料班次", "Line": "产线", "Po": "工单", "Machine": "设备", "Batch": "缸次", "Material": "物料", "IsOver": "是否检查", "Confirmed": "确认人", "ConfirmedDate": "确认时间"}, "INV_FC": {"Batch": "批次", "LineCode": "产线", "Sapformula": "配方", "PlanTime": "计划时间", "Sequences": "序号", "Date/User": "创建日期/创建人", "ProcessOrder(Batch)": "工单（缸次）", "ShiftName": "备料班次", "BatchPallet": "批次托盘", "BatchPallets": "批次托盘", "TraceCode": "追溯码", "PROMaterial": "物料", "Material": "物料", "MaterialPF": "配方料", "Batches": "批", "Quantity": "数量", "Type": "类型", "room": "房间", "Location": "位置", "Expiration": "有效期", "Repeatedweight": "复称重量", "result": "复称结果"}, "INV_YLBQ": {"Remark": "备注", "Sapformula": "配方号", "Bucketnum": "桶号", "index": "序号", "ProcessOrder": "工单", "BNumber": "工单批次", "Material": "物料", "TagerQty": "需求重量", "BagSize": "单包重量", "LotCode": "批次", "SubLotcode": "追溯码", "InventQty": "数量", "FullPage": "包数", "InventPqty": "零头袋", "Expiration": "有效期", "operate": "操作"}, "INV_CLZB": {"Starts": "计划时间", "NowState": "状态", "SAPDate": "工单下发时间", "Sequence": "序号", "JLCode": "酱料编码", "JLName": "酱料名称", "JLNumber": "酱料数量", "Mdetialdesc": "物耗需求", "operate": "操作", "Line": "产线", "FormulaNo": "配方", "LStatus": "产线状态", "SSCC": "追溯码", "ShiftName": "备料班次", "SbStatus": "设备状态", "ProcessOrder": "工单", "Resource": "SAP来源", "POStatus": "工单状态", "Material": "物料", "ProductFamily": "产品组", "Quantity": "数量", "TagertQuantity": "需求数量", "Sequences": "缸次", "Batches": "缸次", "Start": "开始", "Batch": "批次", "Bin": "SAP 位置", "PrepStatus": "备料状态", "TippingDone": "投料完成", "Ingredients": "配料", "Complete": "完成", "SSCC/Batch": "追溯码", "Expiry": "有效期", "TransferDate": "转移日期", "User": "用户", "BatchStatus": "批次状态", "SSCCStatus": "追溯码状态", "SSCC/Container": "追溯码/容器", "Type": "类型", "Location": "位置", "Expiration": "有效期"}, "INV_CLQD": {"LineCode": "产线", "CheakState": "复称", "PO": "工单", "Machine": "设备", "Phase": "工序", "Batch": "缸次", "QuantityRequired": "需求数量", "Material": "物料", "Quantity": "数量", "Inventory": "可用库存", "BagSize": "袋重", "FullBags": "整袋", "PartialBags": "零头袋", "Complete": "完成", "CompleteStates": "状态", "Consumed": "已消耗"}, "PRO_Weight": {"detail": "详情", "Sequence": "序号", "Po": "工单", "Batch": "缸次", "Item": "料号", "Formula": "配方", "ModifyDate": "时间", "ModifyUserId": "操作人", "AverageWeight": "平均重量", "Weight": "重量", "operate": "操作"}, "PRO_Overview": {"Sequence": "序号", "Formula": "配方号", "PlantNode": "工厂节点", "po": "工单", "Material": "物料", "Batch": "批", "BatchQty": "批数量", "Planned": "已计划", "Total": "总数", "Complete": "完成%"}, "Sampling": {"gc": "缸次", "TestingTime": "开始检测时间", "qysj": "取样时间", "rqbh": "容器编号", "gdh": "工单号（批号）", "Formula": "配方号", "sbmc": "设备名称", "cpbm": "产品编码", "rqzt": "容器状态", "glbdzt": "关联表单状态", "Type": "取样类型"}, "PRO_QualityResult": {"operate": "操作", "TestingTime": "开始检测时间", "TestingDuration": "检测时长", "PlanStartTime": "工单生产日期", "Sequence": "煮料序号", "FormulaNo": "配方编码", "CreateDate": "送样（取样）时间", "CreateUserId": "取样人员", "ModifyDate": "检测完成时间", "TestingUser": "检测人", "Formula": "配方号", "Equipment": "设备", "GroupName": "组别名称", "Date": "日期", "Shift": "班次", "Performance": "表现", "PO": "工单", "Batch": "缸次", "SSCC/Container": "追溯码/容器", "Material": "物料", "Score": "得分", "Status": "状态", "Approvals": "放行", "ok": "同意", "Comments": "意见", "SH": "审核"}, "PRO_QAQualityResult": {"operate": "操作", "TestingTime": "开始检测时间", "TestingDuration": "检测时长", "PlanStartTime": "工单生产日期", "Sequence": "煮料序号", "FormulaNo": "配方编码", "CreateDate": "送样（取样）时间", "CreateUserId": "取样人员", "ModifyDate": "检测完成时间", "TestingUser": "检测人", "Formula": "配方号", "Equipment": "设备", "GroupName": "组别名称", "Date": "日期", "Shift": "班次", "Performance": "表现", "PO": "工单", "Batch": "缸次", "SSCC/Container": "追溯码/容器", "Material": "物料", "Score": "得分", "Status": "状态", "Approvals": "放行", "ok": "同意", "Comments": "意见", "SH": "审核"}, "PRO_Logsheets": {"Comment": "意见", "Formula": "配方号", "Date": "日期", "Shift": "班次", "User": "用户", "Status": "状态", "ProcessOrder": "工单", "Supplier": "供应商", "Quantity": "数量", "Code": "编码", "Defectiveproject": "瑕疵品", "Spec": "规格", "Parameter": "参数", "value": "值", "Safety": "限制", "ProdictAttributes": "产品属性", "Instructions": "说明", "Logsheet": "Logsheet", "operate": "操作", "Machine": "机器", "PO": "工单", "CheckResult": "填写状态", "Frequency": "频率", "LastEntry": "最新登录"}, "PRO_POManagement": {"Segment": "产线", "CipDetail": "CIP", "SapDate": "SAP计划日期", "BoilingStatus": "拼锅状态", "Grille": "格筛", "IsHavePreservative": "防腐剂", "BatchCode": "批次号", "Sequence": "序号", "Formula": "配方号", "ProcessOrder": "工单", "Resource": "工作中心", "Material": "物料", "Instruction": "说明", "TargetQuantity": "目标数量", "LineNominalSpeed": "产线标准速度", "ScheduledStart": "计划开始", "Quantity": "数量", "Execution": "执行", "operate": "操作", "start": "开始", "end": "结束", "SAP": "SAP", "NominalSpeed": "标准速度", "User": "操作人", "Comment": "备注", "FillLineCode": "灌装产线"}, "PRO_Consume": {"MaterialLotNo": "指定批次号", "Formula": "配方号", "Operation": "操作", "Type": "类型", "Batch": "缸次", "BatchStatus": "批状态", "SSCCStatus": "追溯码状态", "Expiration": "有效期", "Loaction": "位置", "Select": "选中", "Material": "物料", "Quantity": "数量", "Available": "可用", "StorageBin": "储存仓", "Date": "日期", "BatchCode": "批次", "Shift": "班次", "SSCC": "追溯码", "Destination": "目的地", "Source": "源", "User": "用户", "Comment": "评论", "operate": "操作"}, "PRO_Tipping": {"SortOrder": "投料顺序", "Material": "物料", "Description": "描述", "Required": "必需", "Staged": "预检", "IsOver": "投料前检查是否完成", "Consumed": "消耗", "operate": "操作"}, "PRO_ParameterDownload": {"Step": "步骤", "Equipment": "设备", "Parameter": "参数", "Target": "目标值"}, "PRO_Tippingscan": {"SSCCStatus": "追溯码状态", "SSCC/Container": "追溯码", "Material": "物料", "Group": "分类", "BatchStatus": "批次状态", "Batch": "批次", "Quantity": "数量", "Location": "位置", "Expiration": "有效期"}, "PRO_Performance": {"Formula": "配方号", "Line": "产线", "detail": "详情", "Category": "分类", "Machine": "机器", "SubCategory": "子分类", "Reason": "原因", "Code": "编码", "ProcessOrder": "工单", "CrewSize": "班组人数", "StartTime": "开始时间", "EndTime": "结束时间", "Duration": "运行时间", "Comment": "评论", "Updated": "已更新", "User": "用户", "SourceType": "源数据类型", "Group": "组别", "AMM": "AMM工单", "PO": "工单"}, "PRO_POLIST": {"Landz": "市场", "Ltext1": "BOM文本", "SapDate": "SAP计划日期", "BoilingStatus": "拼锅状态", "Bezei": "规格", "Grille": "格筛", "IsHavePreservative": "防腐剂", "SegmentCode": "产线", "WaitingforQA": "待QA", "QApassed": "通过", "QaStatus": "质检状态", "Formula": "配方号", "ProcessOrder": "工单", "Sequence": "序号", "Source": "工作中心", "Status": "状态", "ShiftName": "工单班次", "Execute": "执行", "Batches": "缸次", "Material": "产品名称", "MaterialCode": "产品编码", "productionversion": "生产版本", "PlanQty": "计划数量", "ActualQty": "实际数量", "ProduceStatus": "工单状态", "Reason": "原因", "PlanStartDate": "计划开始时间", "PlanEndDate": "计划结束时间", "operate": "操作", "CipDetail": "CIP", "FillLineCode": "灌装产线"}, "POLIST_Execute": {"BatchCode": "批次号", "Machine": "机器", "Stage": "阶段", "StageCode": "阶段标识", "Status": "状态", "TargetNum": "目标数量", "StartDate": "开始时间", "EndDate": "结束时间", "User": "操作人", "Comment": "备注"}, "POLIST_Batch": {"operate": "操作", "Batch": "缸次", "Material": "物料", "TargetNum": "目标", "Status": "状态", "readiness": "准备状态", "delete": "删除"}, "POLIST_Produce": {"Stage": "阶段", "Resource": "资源", "Material": "物料", "Quantity": "数量", "ActualProduce": "实际产出", "StorageArea": "存储区"}, "POLIST_parameter": {"Equipment": "设备", "Recipe": "配方", "SectionVersion": "区域[版本]", "ContextType": "文本情景", "Machine": "设备", "Phase": "工段", "Group": "组别", "Parameter": "工艺参数", "Icon": "标志", "ShortDescription": "简短描述", "Limits": "限制值", "Target": "目标值", "Default": "默认值", "UOM": "单位", "Required": "必需", "UpdatedBy": "由谁更新", "ContextVersion": "文本版本"}, "POLIST_consume": {"MaterialLotNo": "指定批次号", "Action": "操作", "Source": "源头", "Stage": "阶段", "Material": "物料", "Quantity": "数量", "AdjustPercentQuantity": "预调值", "Order": "顺序", "StorageArea": "存储区域", "OriginalPhase": "原始工段", "Item": "成品", "ActualConsume": "实际消耗"}, "POLIST_property": {"name": "名称", "value": "值"}, "INV_Storage": {"Workcenter": "工作中心", "detail": "详情", "Material": "物料", "Class": "类型", "BatchStatus": "批次状态", "Precheckestatus": "状态", "Batch": "批次", "SSCCStatus": "追溯码状态", "SSCC/Container": "追溯码", "Quantity": "数量", "Location": "位置", "Expiration": "有效期", "Created": "创建时间", "UOM": "单位", "Lot": "批次", "Result": "结果"}, "SCJX_KPI": {"EvenType": "安全事件类型", "ConfirmDate": "确认时间", "Comment": "备注", "UnitName": "单位", "actions": "操作", "detail": "操作", "SpecCode": "规格编码", "SpecName": "规格名称", "MaterialBigGroup": "物料大分类", "MaterialSmallGroup": "物料小分类", "Saucecategory": "酱料类别", "Target": "目标值", "Name": "数据名称", "Type": "数据类型", "Model": "模型区域", "Year": "年份", "Month": "月份", "Value": "数值", "Unit": "单位", "Line": "生产线", "Spec": "规格", "SpecGroup": "规格群组", "MaterialGroup": "原料群组", "BudgetType": "预算类型", "BudgetOutput": "预算产量"}, "ExperienceLibrary": {"czfa": "处置方案", "cycs": "采用次数", "SolutionDesc": "处置方案", "AdoptionCount": "采用次数"}, "sortBy": "排序方式"}, "dataFooter": {"itemsPerPageText": "每页数目：", "itemsPerPageAll": "全部", "nextPage": "下一页", "prevPage": "上一页", "firstPage": "首页", "lastPage": "尾页", "pageText": "{0}-{1} 共 {2}"}, "datePicker": {"itemsSelected": "已选择 {0}", "nextMonthAriaLabel": "下个月", "nextYearAriaLabel": "明年", "prevMonthAriaLabel": "前一个月", "prevYearAriaLabel": "前一年"}, "noDataText": "没有数据", "carousel": {"prev": "上一张", "next": "下一张", "ariaLabel": {"delimiter": "Carousel slide {0} of {1}"}}, "calendar": {"moreEvents": "还有 {0} 项"}, "fileInput": {"counter": "{0} 个文件", "counterSize": "{0} 个文件（共 {1}）"}, "timePicker": {"am": "AM", "pm": "PM"}, "pagination": {"ariaLabel": {"wrapper": "分页导航", "next": "下一页", "previous": "上一页", "page": "转到页面 {0}", "currentPage": "当前页 {0}"}}, "rating": {"ariaLabel": {"icon": "Rating {0} of {1}"}}, "SBKB_SBRW": {"Loaction": "位置", "EquipmentName": "设备名称", "McProject": "点检项目", "status": "状态", "PersonName": "责任人"}}, "Inventory": {"Delivery": "交仓", "NotSend": "未发送", "BeenSent": "已发送", "WorkShop": "存储位置", "NotFound": "未找到转换", "bagstags": "该计算结果仅供参考，具体以WMS创建结果为准!", "Formula": "配方号", "Bucketnum": "桶号", "ClearLine": "清线", "UpDateBatch": "更新批次号", "ImgOnly": "只能上传图片", "ChooseDestination": "请选择目的地", "Batch": "批次", "MaterialCode": "物料编码", "BatchStatus": "批次状态", "SSCCStatus": "追溯码状态", "Location": "位置", "SAPLoaction": "SAP 位置", "Container": "容器", "ProductionDate": "产出日期", "showProcessCode": "展示工单", "refresh": "刷新", "scrap": "不良品退仓", "Return": "退仓", "QualityReturn": "质量退库", "Transfer": "转移", "AddInventory": "添加库存", "Remark": "添加备注", "Class": "物料类型", "Material": "物料", "SSCC": "追溯码", "Quantity": "数量", "expirationdate": "有效期", "fullbagweight": "单包重量", "unit": "单位", "bags": "包数", "validate": "确认有效", "InventoryDetails": "库存明细", "InventoryBlock": "库存锁定", "InventoryUnBlock": "库存解锁", "Split": "拆分", "Block": "批次上锁", "UnBlock": "批次解锁", "UnBlock2": "解锁", "Print": "打印", "CreateWMS": "生成WMS标签", "Type": "类型", "FullBag": "整袋", "Comment": "评论", "Destination": "目的地", "selectprinter": "选择打印机", "Reprintsourcelabel": "打印源标签", "Lot": "批次", "offullbags": "整袋数量", "BagSize": "规格", "SplitQuantity": "拆分数量", "BlockSure": "确定锁定此条库存吗？", "UnBlockSure": "确定解锁此条库存吗？", "MergeInventoryInto": "将库存合并到", "leasttwo": "至少选择两条数据！", "ToOver": "*为必填信息！", "PleaseSelectPrinter": "请选择打印机！", "ToGetSSCC": "请获取追溯码", "QuantityOver": "分装数量不能大于原有数量", "ReverseOver": "反冲数量不能大于原有数量", "Inspection": "质检"}, "ProductionHistory": {"Machine": "机器", "scan": "追溯码扫描", "ScanContainerCode": "扫描追溯码", "Material": "物料", "Batch": "缸次", "Batchs": "批次", "SSCC": "追溯码", "ProcessOrder": "工单", "Container": "容器", "MaterialClass": "物料类型", "Operator": "操作人员", "Destination": "目的地", "Comment": "评论", "ReasonCode": "原因编码", "Reason": "原因", "Line": "产线", "Shift": "班次", "Quantity": "数量", "SSCCOnly": "仅追溯码", "NonSSCC": "无追溯码", "SAPMessageStatus": "SAP状态", "ReverseProduce": "反冲产出", "ConsumProduce": "消耗反冲", "Reverse": "反冲", "ReverseQuantity": "反冲数量"}, "ProductionSummary": {"Productionsbyshift": "按班次显示", "Machine": "机器", "ProcessOrder": "工单"}, "ConsumptionHistory": {"IsSauce": "是否酱料", "Machine": "机器", "Material": "物料", "Batch": "批次", "SSCC": "追溯码", "ProcessOrder": "工单", "Container": "容器", "MaterialClass": "物料类型", "Operator": "操作人员", "Destination": "目的地", "Comment": "评论", "ReasonCode": "原因编码", "Reason": "原因", "Line": "产线", "Shift": "班次", "Quantity": "数量", "SSCCOnly": "仅追溯码", "NonSSCC": "无追溯码", "NoBatchId": "请选择缸次", "SAPMessageStatus": "SAP状态", "ReverseProduce": "产出反冲", "ReverseConsum": "消耗反冲", "Reverse": "反冲", "ReverseQuantity": "反冲数量", "Source": "设备来源", "ConsumedQuantity": "消耗数量", "SourceBin": "SAP 位置来源"}, "TransferHistory": {"SourceMaterial": "源物料", "DestinationMaterial": "目的地物料", "SourceBatch": "源批次", "DestinationBatch": "目的地批次", "SourceSSCC": "源追溯码", "DestinationSSCC": "目的地追溯码", "Source": "来源", "SourceBin": "SAP位置来源", "Destination": "目的地", "DestinationBin": "SAP位置目的地", "SAPMessageStatus": "SAP状态", "OldProBatch": "源订单号", "NewProBatch": "目地订单号", "ConOldName": "源容器号", "ConNewName": "目地容器号", "Type": "类型"}, "ContainerManagement": {"ContainerId": "容器编号", "ContainerClasses": "容器类型", "Statues": "状态", "Material": "物料", "Location": "位置", "Batch": "批", "SSCC": "追溯码", "ProcessOrder": "工单", "Transfer": "转移", "Containers": "容器", "ChangeStatus": "变更状态", "Empty": "清空", "Details": "明细", "Inventory": "库存", "History": "历史", "Name": "名称", "Destination": "目的地", "Groups": "分组", "LastMaterial": "最后一种物料", "Class": "类型", "Comment": "评论", "Status": "状态", "CycleCount": "使用次数", "TareWeight": "皮重", "MaxWeight": "最大重量", "ContentWeight": "净重", "BinSLOC": "SAP位置", "Container": "容器", "CurrentStatus": "当前状态", "CurrentWeight": "当前重量", "NewStatus": "新状态", "TransferContainer": "转移容器", "AddInventory": "添加库存", "RemoveInventory": "废料库存", "Delete": "删除"}, "SparePart": {"Type": "类型", "Subject": "备件科目", "Code": "备件编码", "Name": "备件名称", "PartType": "备件类型", "Model": "规格型号", "LowerBound": "安全库存下限", "UpperBound": "安全库存上限", "ServiceLife": "使用寿命", "PurchaseCycle": "采集周期（天）", "MinPurchaseQty": "最低采购数", "Unit": "单位", "Remark": "备注"}, "PalletList": {"AutoRefreshseconds": "自动刷新时间（30s）", "Destination": "目的地", "Area": "区域", "VerifiedStatus": "确认状态", "Scantoverify": "扫码确认", "Verified": "已确认", "Verify": "确认", "Reprint": "重新打印", "PalletDetails": "托盘明细", "Material": "物料", "Type": "类型", "BatchStatus": "批次状态", "SSCCStautus": "追溯码状态", "Quantity": "数量", "StorageBin": "存储SAP位置", "ExpirationDate": "有效期", "PrintCount": "打印数量", "Block": "锁定", "ReverseProduce": "产出反冲", "Reverse": "反冲", "ProcessOrder": "工单", "Batch": "批次", "SSCC": "追溯码", "ReverseQuantity": "反冲数量", "Reason": "原因", "SelectPrinter": "选择打印机", "Comment": "评论"}, "BatchPallets": {"BatchPalletId": "批次托盘ID", "ProcessOrder": "工单", "ShowCompleted": "显示已完成", "Location": "位置", "Status": "状态", "Bin": "SAP 位置", "Machine": "机器", "HideEmptyBatchPallets": "隐藏空批次托盘", "Transfer": "转移", "Reassign": "重新分配", "Destination": "目的地", "Id": "ID", "PO": "工单", "QuickSearch": "快速搜索", "Batch": "缸次", "Contents": "内容", "TransferHistory": "转移历史", "NewPoBatch": "创建批", "ReassignBatchPallet": "重新分配批次托盘", "TransferBatchPallet": "转移批次托盘", "ReprintPalletLabel": "重新打印托盘标签", "ReprintLabel": "重新打印包标签", "TransferPallet": "转移托盘", "RemoveBag": "移除包"}, "MaterialPreparation": {"NoSame": "选中的数据不一致请重新选择", "QuickSearch": "快速搜索", "Over": "数量不能大于单包重量！", "Lot": "请输入批次号", "Line": "产线", "ProductFamily": "产品组", "ShiftName": "班次", "MaterialCode": "物料", "Room": "房间", "CheckRoom": "请选择房间"}, "Repeatedweighing": {"Target": "目标值", "Default": "默认值", "Weight": "称读值", "Defaultconfirm": "默认值确认", "RepeatedInventory": "复称库存", "result": "复称结果", "tip": "扫描或选择库存信息.选择传送类型,输入#袋子,袋子重量,然后点击'传送'按钮", "Difference": "差值", "ProductionOrderNo": "生产工单", "BatchCode": "批号", "confirm": "复称确认"}, "MaterialPreparationBuild": {"PLCJTP": "批量创建托盘", "NoTransfer": "该状态无法转移", "NoMaterial": "没有下个物料", "OpenKeyDown": "开启键盘监听", "CloseKeyDown": "关闭键盘监听", "Addsmalllabels": "补打小标签", "PartialBagMerge": "合并", "POTransfer": "转移", "zts": "总托数", "dqgxh": "当前缸序号", "ts": "托数", "jsgs": "水量备注", "xzdyj": "选择打印机", "FormulaNo": "配方号", "OverExpirationDate": "超出有效期", "InQuantityNotEnough": "选中库存不足!", "QtyOverMax": "数量超过最大值", "BatchPalletsEmpty": "请先添加批次托盘!", "ByBatch": "按工单", "ByMaterial": "按物料", "POSelection": "工单选择", "Operate": "操作", "Addpallets": "添加托盘", "ReprintBtn": "重新打印", "Reprint": "重新打印批次托盘", "Deletepallets": "删除托盘", "FullBags": "整袋", "RemoveBags": "移除袋子", "Currentmaterialonly": "只看当前物料", "CompletePallet": "完成托盘", "OpenPallet": "打开托盘", "BatchPallets": "批次托盘", "BagWeight": "袋子重量", "FullBag": "整袋", "Bags": "# 袋数", "Selectthematerialtobeprepared": "选择需要准备的物料", "SelectMaterial": "选择物料", "SelectBatch": "选择批", "Transfer": "转移", "Merge": "合并", "Remaining": "剩余", "scale": "比例尺", "OverWrite": "复写", "printer": "打印机", "ChooseScale": "选择秤", "PartialBag": "零头袋", "FullAmount": "整批", "PO": "工单", "Batch": "缸次", "Partial": "部分", "AvallableInventory": "可用库存", "AvallableInventoryPrint": "可用库存打印", "Min": "最小值", "Actual": "实际值", "Target": "目标值", "Max": "最大值", "Previous": "向前", "MaterialTransfer": "物料转移", "POInventory": "工单库存", "NextMaterial": "下一物料", "AddPallet": "添加托盘", "IngredientSelection": "原料选择", "tiptitle": "扫描或选择一个追溯码. 选择转移类型, 输入 #袋数, 袋重, 然后点击 '转移' 按钮.", "BatchSelection": "选择批", "Selectthebatchesingredienttobeprepared": "选择需要制备的批原料", "Selectthebatchestobeprepared": "选择需要制备的批次工单", "Hidecompleted": "隐藏已完成", "Ingredients": "原料", "buildpallets": "下一步", "OverRide": "覆写"}, "PAGINATION": {"TOTAL_CN": "", "TOTAL": "总数", "PER_PAGE": "每页", "GO_TO": "跳转至:", "OF": "of", "PAGE": "页", "MYPAGE": "#/页"}, "Overview": {"CGBM": "储罐编码", "GC": "缸次", "SampleWeighing": "平均重量计算", "POList": "工单列表", "Sequence": "序号", "TextGood": "长文本比对通过", "IsUpdateLtxt": "工单工艺长文本与最新配方长文本不一致。", "Comments": "备注", "CommentsOption": "备注选择", "ProcessText": "工艺长文本", "QrCode": "QrCode", "SendColos": "发送Colos", "ParameterDownload": "参数下发", "BatchCodeLong": "批次码需要小于或等于十位", "Logsheets": "表单", "Material": "物料", "Overview": "预览", "Performance": "表现", "PerformanceEvents": "事件收集", "Back": "返回", "LotCode": "批次编码", "Tipping": "投料", "Storage": "储存", "POManagement": "工单管理", "Produce": "生产", "Consume": "消耗", "ActiveOrders": "激活工单", "AvailableOrders": "可用订单", "History": "历史数据", "QuickSearch": "快速检索", "status": "状态", "start": "开始", "Batch": "缸次", "NoActiveProcessOrder": "没有激活工单", "ChooseEquipment": "选择设备", "StartOrder": "开始工单", "StartTime": "开始时间", "BatchCode": "批次编码", "ProductionDate": "批次日期", "ExpirationDate": "有效期", "TargetQuantity": "目标数量", "CrewSize": "班组人数", "bottleneck": "瓶颈", "Start": "开始", "Cancel": "取消", "Note1": "注意: 该机器已经开始运行选中工单 .", "Note2": "如果您继续，工单会自动停止", "Stop": "停止", "Hold": "暂停", "Resume": "继续", "UpdateOrder": "更新工单", "AutoReport": "消耗报工", "UpdateRemark": "更新备注", "NextBatch": "下一批", "EndTime": "结束时间", "CompletePO": "完成工单", "StopNote": "您确定停止选中的执行吗?", "HoldNote": "您确定暂停选中的执行吗?", "UpdateNote": "更新运行工单 ", "DefaultBatchCode": "默认批次编码 "}, "Consume": {"Scan": "扫描", "Select": "选择", "Search": "搜索", "Source": "源", "activation": "激活", "NotActive": "不激活", "Consume": "消耗", "Required": "必需", "Consumptions": "消耗", "Remaining": "剩余", "Uom": "单位", "Transfer": "转移", "SSCC": "追溯码", "ProcessOrder": "工单", "Location": "位置", "Material": "物料", "StorageBin": "存储仓", "Over2": "消耗数量不能大于剩余数量！", "Over": "消耗数量不能大于库存数量！", "Produced": "生产", "Lot": "批次", "Quantity": "数量", "Back": "返回", "Produce": "产出", "ProductionDate": "生产日期", "ExpirationDate": "有效期", "Batch": "缸次", "printlabel": "打印标签", "selectprinter": "选择打印机"}, "Processlongtext": {"Release": "释放"}, "POList": {"Calculate": "开始计算", "Reopen": "重新打开工单", "NotFoundPO": "未找到工单", "SourceStroage": "来源储罐", "ThroatOutput": "喉头产出", "WCSUpDate": "WCS更新", "SegmentCode": "产线", "PleaseSelect": "请选择", "WaitingforQA": "待QA", "QApassed": "通过", "QaStatus": "质检状态", "Inspection": "质检", "PlanQty": "计划产量", "Quantity": "计划产量", "ActualQty": "实际产量", "ProduceStatus": "工单状态", "NotComplete": "工单未完成", "OverComplete": "工单过量完成", "CompleteAtOnce": "工单一次性完成", "Reason": "原因", "AddMaterialPreShift": "添加备料班次", "MaterialPreShift": "备料班次", "CHECKMaterialPreShift": "选择备料班次", "property": "属性", "Processlongtext": "工艺长文本", "EditTitle": "生产工单修改", "Edit": "修改", "ProductionOrderNo": "工单", "Source": "SAP资源", "Material": "物料", "MaterialVersion": "产品版本", "PlanStartTime": "计划开始时间", "PlanEndTime": "计划结束时间", "Speed": "额定速度", "KGHOUR": "KG/小时", "Status": "状态", "Num": "数量", "Release": "释放", "Go": "放行", "Complete": "完成工单", "RevokeRelease": "撤销释放", "constructingbatches": "重新解析构建批", "BindPoRecipe": "绑定配方", "Execute": "执行", "batch": "缸次", "consume": "消耗", "formula": "配方参数表", "produce": "生产", "warningText": "工艺长文本与最新配方长文本不一致，请确认工艺长文本与工艺参数。", "parameter": "表单列表", "ProcessOrder": "工单", "Available": "可用的"}, "POListBatch": {"AddBatch": "添加缸次", "stage": "阶段", "BatchNum": "缸数", "BatchSize": "缸重", "MinimumSize": "最小重量", "MaximumSize": "最大重量", "CopyBatch": "确定复制此缸次吗?", "DeleteBatch": "确定取消该缸次吗?", "ChangeBatch": "确认完成备料？"}, "POListparameter": {"Recipe": "配方", "Dom": "配方部分", "ShowContextDetails": "显示上下文详细信息", "UpdateFromEffective": "有效更新"}, "ParameterDownload": {"DomTable": "配方表", "ParameterTable": "参数表", "Parameter": "参数", "Target": "目标值"}, "Tippingscan": {"MaterielNumber": "物料数量", "BarCode": "追溯码", "NotSelect": "未选中", "Tipping": "投料", "Dock": "扫描", "Destinationselect": "选择目的地", "PZSdockacontainter": "请扫描库存条码", "DockedContainerInformation": "已扫描库存条码", "Destination": "目的地", "AvailableInventory": "可用库存"}, "POListconsume": {"Expandall": "全部展开", "Collapseall": "全部收起", "showempty": "显示空白"}, "POSampleWeighing": {"Scale": "读数", "SampleNumber": "取样数量", "SampleWeighing": "记录读数"}, "POListTipping": {"Tipping": "投料", "TippingOver": "投料完成", "ForcedCompletion": "强制完成", "StartTipping": "开始投料", "Scan": "扫描", "TippingText": "点击'开始投料'按钮来开始投料"}, "POLogsheets": {"QaConfirm": "QA确认", "hdlr": "核对内容", "tgjg": "请选择通过结果!", "OverLimit": "超出限制!", "NeedtoComments": "请填写评论!", "Reopen": "重新打开", "UpdatedAt": "更新于", "LogsheetImages": "表单名称", "EntryList": "输入列表", "IncludePhoto": "包含图片", "Comment": "意见", "Complete": "完成", "CompleteClose": "完成并关闭", "Date": "日期", "Status": "状态", "More": "更多", "Void": "无效", "SaveComment": "保存意见", "Dashboard": "任务表", "GotoLatest": "跳转至最新", "Entry": "输入", "NewEntry": "最新输入", "ExternalUrl": "外部URL"}, "QualityResult": {"Status": "表单状态", "Group": "组别", "ProcessOrder": "工单", "Material": "物料", "Batch": "缸次", "InpectionLotRelated/Unrelated": "检测点关联/不关联", "NoGrouping": "无分组", "TLApprove": "主管放行", "QAApprove": "质检放行", "ShowEntrieswithoutValues": "显示没有值的条目", "ShowOnlyFailedEntries": "只显示失败条目", "ShowEntriesPendingVerificationOnly": "仅显示待验证条目"}, "POPerformance": {"Event": "可用事件", "Category": "原因大类", "MonthlySettlementReport": "月结报工", "Reason": "原因", "PO": "工单", "Shift": "班次", "EquipmentStatus": "设备状态", "POStatus": "工单状态", "ByReason": "按原因", "ByReckassification": "重新定义", "Running": "运行", "Overtip": "创建此事件将修改或覆盖以下事件。请确认您希望继续。", "OverwritingEvents": "覆盖事件", "PZSSelectedreason": "请选择原因", "Selectedreason": "已选择原因", "Comments": "意见", "StartTime": "开始时间", "Duration": "持续时间", "EndTime": "结束时间", "ActualLabourMinutes": "实际劳动分钟", "SelectCode": "请选择一个原因编码", "AllEvent": "全部事件", "MergeEvent": "合并事件", "SplitEvent": "拆分事件", "SelectOne": "请选择主事件", "Line": "产线", "Machine": "机器", "New": "新增", "CrewSize": "班次人数", "Reclassify": "重新定义", "Delete": "删除", "NewEvent": "新事件", "EditEvent": "编辑事件", "Merge": "合并", "EventCollection": "事件收集", "ON": "开", "Off": "关", "EventHistory": "事件历史", "HistoryofEventChanges": "事件变换历史", "MergeTitle": "选择要合并到的主事件"}, "precheck": {"groupBy": "分组依据", "BatchPallet": "批次托盘", "now": "目前的", "redistribution": "再分配", "TraceCode": "追溯码", "all": "全部的", "bag": "袋", "beginPrecheck": "开始预检查", "PrecheckOver": "预检查完成", "Precheck": "预检", "scan": "扫描", "orderBatch": "缸次"}, "PrecheckFeeding": {"ScanContainerCode": "扫描备料大标签", "BatchPalletId": "批次托盘ID", "BatchContainerId": "批次容器ID", "LineName": "产线", "Machine": "机器", "ProcessOrder": "工单", "ShowCompleted": "显示已完成"}, "Feeding": {"tips": "请输入物料名称或代码", "NeedQuantity": "需求数量", "InventQty": "库存数量", "ActualQuantity": "已请料库存", "ShQuantity": "已收货库存", "batch": "批次", "zzgd": "制造工单", "fzzgd": "非制造工单", "Detail": "详情", "Type": "请料类型", "TraceCode": "追溯码", "Area": "区域", "Status": "状态", "Quantity": "数量", "add": "创建", "FeedingAdd": "请料创建", "Material": "物料", "MaterialType": "物料类别", "AddByMaterial": "添加指定料", "AddByDate": "添加时间段", "ChooseDate": "选择时间段", "Delete": "移除"}, "FeedingFULL": {"NeedQuantity": "需求数量", "InventQty": "库存数量", "ActualQuantity": "已拉料库存", "ShQuantity": "已收货库存", "batch": "批次", "zzgd": "制造工单", "fzzgd": "非制造工单", "Detail": "详情", "Type": "拉料类型", "TraceCode": "追溯码", "Area": "区域", "Status": "状态", "Quantity": "数量", "add": "创建", "FeedingAdd": "拉料创建", "Material": "物料", "MaterialType": "物料类别", "AddByMaterial": "添加指定料", "AddByDate": "添加时间段", "ChooseDate": "选择时间段", "Delete": "移除"}, "Storage": {"Receipt": "收货", "Sugarpretreatment": "预处理", "Scan": "扫描收货", "Receiving": "收料", "empty": "追溯码不能为空", "noSscc": "未找到该追溯码"}, "Formula": {"title": {"Production_scheduling": "配方排产", "CIPCeaning": "CIP清洗时间维护", "CipSwitch": "CIP切换方式", "BoilingTankCapacity": "产线的煮缸容量", "RecommendedFormulaCylinderCapacity": "产线配方以及缸容量推荐", "BOMInjection": "工艺联通", "PlannedWork": "生产计划", "ResourceDefinition": "源头定义", "SectionProcessDefinition": "工段工序定义", "ProcessAndEquipmentAssociation": "工序与设备关联", "ProductBOMDefinition": "产品BOM定义", "ProductBOMMaterialDetails": "产品BOM物料明细", "CylinderWeightMaintenance": "特殊配方对应缸重维护"}, "Cylinder_Weight_Maintenance": {"LineName": "产线", "MatName": "配方", "SapFormula": "配方代码", "MtrCode": "物料编码", "ContainerName": "规格", "ContainerCode": "规格编码", "TargetlineName": "目标产线", "Weight": "推荐缸容量", "Targetweight": "目标缸重"}, "Production_scheduling": {}, "CIP_Ceaning": {"LineName": "产线", "Switchname": "切换方式", "Switchtime": "切换时间(分钟)"}, "Boiling_Tank_Capacity": {}, "Recommended_Formula_Cylinder_Capacity": {"LineName": "产线", "FormulaCode": "配方", "MatCode": "配方编码", "MatName": "配方描述", "ContainerCode": "瓶型", "ContainerName": "瓶型描述", "Recommandsize": "推荐缸容量(KG) "}, "Cip_Switch": {"PrematName": "前配方", "PrematCode": "前配方代码", "PreMaterialCode": "前配方物料代码", "PostmatName": "后配方", "PostmatCode": "后配方代码", "PostMaterialCode": "后配方物料代码", "Switchname": "切换方式"}, "Throataddition": {"FormulaCode": "喉头配方", "MaterialCode": "喉头物料代码", "MatName": "喉头物料名称", "AddableFormulaCode": "可添加到配方", "AddableMaterialCode": "可添加到配方物料代码", "MatAddableName": "可添加到配方物料名称", "Rate": "添加比例"}, "Cooking_Loss": {"LineName": "产线", "MatCode": "配方", "MtrCode": "物料编码", "MatName": "配方名称", "TankCapacity": "缸容量(t)", "TankwallLose": "煮缸挂壁损耗(kg)", "PipeLose": "管道损耗(kg) ", "ReservoirLose": "储缸挂壁损耗(kg) ", "PertankLose": "每缸煮料损耗(kg)"}, "Formula_Loss": {"LineName": "产线", "MatName": "配方", "TankWeight": "标准缸重", "Tankquantity": "锅数 ", "EstimateLoss": "预计损耗 ", "Productdate": "生产日期 "}, "BOM_Injection": {"MaterialGroup": "物料组", "MaterialCode": "物料编码", "MaterialName": "物料名称", "MaterialVersionNumber": "物料版本号", "ProductSapSegmentName": "产出工序", "ConsumeSapSegmentName": "消耗工序", "ProductMaterialName": "产出物料", "ProductMaterialUnitId": "产出物料单位", "PercentQuantity": "转换率", "Priority": "优先级", "ConsumeQuantity": "消耗数量"}, "Resource_Definition": {"Code": "编码", "Name": "名称"}, "Resouce_Operation": {"SegmentName": "工段", "Remark": "备注"}, "Resouce_Phase": {"SegmentName": "工序", "Remark": "备注"}, "Resouce_Phase_Sales": {"SalesContainer": "销售规格", "Description": "描述"}, "Resouce_Phase_Machine": {"SapSegmentName": "工序名称", "EquipmentName": "设备名称"}, "Section_Process_Definition": {"ID": "ID", "SapEquipmentId": "SAP资源ID", "SegmentCode": "编码", "SegmentName": "名称", "Remark": "备注", "CreateDate": "创建时间"}, "Process_And_Equipment_Association": {"EquipmentId": "设备ID", "SapSegmentId": "工序ID", "CreateDate": "创建时间"}, "Product_BOM_Definition": {"Unit": "单位", "MaterialCode": "产品编码", "MaterialName": "产品名称", "MaterialVersionNumber": "版本号", "SapEquipmentCode": "资源", "Operation": "工段", "Phase": "工序"}, "Product_BOM_Material_Details": {"SortOrder": "投料顺序", "Material": "物料", "Unit": "单位", "ParentQuantity": "标准产量", "Quantity": "标准量", "AdjustPercentQuantity": "预调值", "IsSubcontract": "是否需要分包", "FeedPort": "投料口", "LineWarehouseCode": "产线叫料目的地", "CreateDate": "创建时间", "ModifyDate": "更新时间", "ModifyUserId": "最近更新人"}, "Preprocess_Text": {"TextVersion": "版本", "ProcessData": "原料预处理文本", "Status": "状态", "CreateUserId": "创建人员", "CreateDate": "创建时间", "Reviewuserid": "确认人员", "Reviewtime": "确认时间"}, "Plant_Reason_Codes": {"Description": "描述", "Type": "类型", "Status": "状态"}, "Line_Configuration": {"LineName": "产线", "Machines": "设备", "LineCode": "产线编码"}, "Line_Configuration_Expand": {"EquipmentName": "设备", "ManualData": "手动编码", "AutoCount": "自动编码"}, "Auto_Matic": {"ReasonDescription": "原因描述", "PlcCode": "PLC编码", "PlcCodeDescription": "PLC编码描述", "CategoryDescription": "种类", "GroupDescription": "原因类别", "IsAutoCreateRepairOrder": "自动创建维修工单", "IsAndonAralm": "是否Andon报警", "ModifyDate": "最新更新", "ModifyUserId": "用户"}, "Manual": {"ReasonDescription": "描述", "GroupDescription": "组别", "CategoryDescription": "种类", "Status": "状态", "Quick Link": "快捷链接"}, "Work_Hour_Report": {"ProductionOrderNo": "工单号码", "SegmentName": "工作中心", "Resource": "产线", "Formula": "配方号", "Description": "描述", "GoodCount": "实际产量", "Unit1": "实际产量单位", "WasteCount": "废品数量", "Unit2": "散支单位", "Code": "编码", "PlanQty": "计划数量", "PoStatus": "工单状态", "Status": "发送状态", "TotalCount": "总数", "CovGoodCount": "转换后实际产量", "CovUnit": "转换后单位", "EquipmentCodes": "设备", "Msg": "sap返回结果", "SendTime": "第一次发送时间", "SendTime2": "最后一次发送时间", "Status2": "反冲发送状态", "CONF_NO": "凭证号", "CONF_CNT": "行号", "CONF_TEXT": "反冲原因", "Msg2": "反冲sap返回结果", "CreateDate": "创建日期"}, "Work_Hour_Report_Cook": {"ProductionOrderNo": "工单号码", "Formula": "配方号", "Sequence": "序号", "MaterialCode": "产品编码", "MaterialDescription": "产品", "LineCode": "产线", "PlanQty": "计划数量", "PoStatus": "工单状态", "Status": "发送状态", "Unit": "单位", "Msg": "sap返回结果", "SendTime": "第一次发送时间", "SendTime2": "最后一次发送时间", "Status2": "反冲发送状态", "CONF_NO": "凭证号", "CONF_CNT": "行号", "CONF_TEXT": "反冲原因", "Msg2": "反冲sap返回结果", "CreateDate": "创建日期"}, "Material_Mapping": {"Code": "料号", "Description": "描述", "MappingType": "类型", "Type": "包括/排除"}, "Capacity": {"Material": "物料", "MaxQuantity": "最大容量", "ModifyUserId": "由谁更新", "ModifyDate": "更新日期"}, "Auxiliary_Working_Hours": {"EquipmentName": "设备", "GroupName": "原因分组", "ReasonName": "原因", "StartTime": "开始时间", "EndTime": "结束时间", "Duration": "持续时长", "CrewSize": "班组人数", "ActualLaborMinutes": "实际工时", "Remark": "备注"}, "LineRelation": {"LineCode": "灌注线Code", "LineName": "灌注线", "PrelineCode": "煮制线Code", "PrelineName": "煮制线"}, "BoilingTankOutput": {"MaterialName": "物料名称", "MaterialCode": "物料编码", "TankCapacity": "缸重", "MaxWeight": "最大重量", "MinWeight": "最小重量"}, "ChangeModel": {"LineCode": "产线代码", "LineName": "产线名称", "ChangeModel": "换型模式", "Changetime": "换型时间(分钟)"}, "ProductSpeed": {"Wccode": "工作中心", "MatName": "配方ID", "MatCode": "配方代码", "SalescontainerCode": "容器代码", "Schedulespeed": "排产速度", "Oeespeed": "OEE速度", "Uom": "单位"}, "OilFormula": {"SapFormula": "配方", "FormulaMaterialCode": "配方物料代码", "FormulaMaterialName": "配方名称", "OilMaterialCode": "油物料代码", "OilMaterialName": "油物料名称", "Rate": "比例", "Remark": "备注"}}, "PlanWork": {"title": {"PlannedWork": "生产计划"}, "button": {"PackagingWorkOrder": "获取包装工单", "FormulaMerge": "同配方合并", "FormulaSplit": "拆分PV", "ModifyBOM": "修改PV", "GeneratePlanOrder": "生成计划工单", "UploadSAP": "手动上传SAP", "QueryBOM": "查询PV", "Query": "查询"}, "Packaging_Work_Order": {"a": "最大入料重量", "a1": "配方", "a2": "工单号", "a3": "工作中心", "a4": "产品编码", "a5": "MRP", "a6": "产品名称", "a7": "半成品数量", "a8": "批号", "a9": "生产数量", "a10": "单位", "a11": "实际生产数量", "a12": "销售订单", "a13": "走柜期", "a14": "备注"}, "Formula_Merge": {"ProduceDate": "计划日期", "SapOrderNo": "SAP计划工单", "MesOrderNo": "MES计划单号", "LineName": "产线", "SapFormula": "配方", "ProdVersionText": "PV版本", "Planweight": "计划重量(KG)", "SapContainer": "规格", "SapStatus": "操作状态", "SapFlag": "同步SAP状态"}, "Formula_MergeCopy": {"SapOrderNo": "SAP计划工单", "SapStatus": "SAP状态", "ProduceDate": "日期", "MesOrderNo": "计划工单号", "LineName": "生产线", "SapContainer": "规格", "SapFormula": "配方", "MatCode": "物料编码", "MatName": "配方名称", "ProdVersionText": "PV版本", "Planweight": "计划表重", "Scheduleweight": "排产重量", "Lossweight": "损耗", "Inlightweight": "入轻", "Adjustweight": "调节", "Throatweight": "喉头"}, "Edit_BOM_Version": {"Matnr": "配方号", "SapFormula": "配方代码", "Verid": "物料版本", "Text1": "描述"}, "Formula_Split": {"a": "SAP状态", "a1": "日期", "a2": "计划工单号", "a3": "生产线", "a4": "规格", "a5": "配方名称", "a6": "配方", "a7": "物料编码", "a8": "BOM版本", "a9": "计划表重"}, "Modify_BOM": {"a": "物料编号", "a1": "物料名称", "a2": "执料量", "a3": "煮料量", "a4": "备注"}, "Generate_Plan_Order": {"a": "日期", "a2": "计划工单号", "a3": "生产线", "a4": "规格", "a5": "配方名称", "a6": "配方", "a7": "物料编码", "a8": "BOM版本", "a9": "计划表重", "a10": "损耗", "a11": "喉头", "a12": "调节", "a13": "入轻", "a14": "排产重量", "a15": "缸数", "a16": "每缸数量", "a17": "总和", "a18": "备注"}, "OrderThroat": {"PlanDate": "计划日期", "LineCode": "煮制线", "ProductionOrderNo": "SAP工单号", "OrderCode": "MES工单号", "PoSapFormula": "工单配方", "PlanQty": "工单计划量", "SapFormula": "喉头配方", "ThroatCode": "喉头物料编号", "MatName": "喉头名称", "Throatformorder": "批号", "SSCC": "追溯码", "Inweight": "计划添加量(KG)", "Rate": "添加比例", "StandardRate": "标准最大比例"}}, "ProductionWork": {"title": {"ProductionWork": "制造生产工单管理"}, "button": {"GenerateLargeTable": "生成大表", "GenerateProdTable": "生成排产表", "EditProdTable": "排产表调整", "GenerateWorkOrder": "生成工单", "MesWorkOrder": "生成MES工单", "PreliminaryProdSchedule": "自动排序", "RecipeSort": "手动排序", "RobotMotion": "节拍计算", "FormulaThroatDetail": "配方喉头明细", "LossSelection": "损耗选择", "EditWorkOrder": "修改工单", "OrderSearch": "工单查询", "SAPUpload": "上传SAP", "PublicWorkOrder": "发布工单", "CreateWorkOrder": "创建工单", "QueryBom": "查询BOM", "CancelWorkOrder": "取消工单", "SapBom": "同步Sap-Bom", "BomBz": "Bom备注", "SonWorkOrder": "子工单"}, "AddSonRemarks": {"MtrName": "物料名称", "MaterialCode": "物料编码", "Remark": "备注"}, "SonWorkOrderThroat": {"MtrName": "物料名称", "MaterialCode": "物料编码", "Remark": "备注"}, "Material_Info": {"ProduceDate": "日期", "FillLineName": "产线", "LineName": "煮制线", "SapContainer": "规格", "CipMethod": "Cip记录", "SapFormula": "配方代码", "MatCode": "物料编码", "MatName": "配方名称", "Sequence": "序号", "GeShai": "隔筛", "FangFuJi": "防腐剂", "ProdVersionText": "版本", "Planweight": "计划重量", "NewQty": "最新计划重量", "differValue": "差值", "Scheduleweight": "排产重量", "TankquantityText": "缸数", "TankweightText": "每缸重量", "Lossweight": "损耗", "Inlightweight": "入轻", "Throatweight": "喉头", "Adjustweight": "调节"}, "workOrderDetail": {"LineName": "生产线", "MatCode": "物料编码", "MatName": "配方名称", "Planweight": "计划重量", "Lossweight": "损耗", "Throatweight": "喉头", "Adjustweight": "调节", "Inlightweight": "入轻", "Scheduleweight": "排产重量", "TankquantityText": "缸数", "TankweightText": "每缸重量", "ProduceDate": "日期", "ProdVersion": "版本", "SapFormula": "配方代码", "SapContainer": "规格", "CipMethod": "Cip记录", "Sequence": "序号", "FillLine": "灌装线", "Remark": "备注"}, "Work_Order_Info": {"date": "计划日期", "FillLine": "产线", "LineCode": "煮制线", "SegmentCode": "工作中心", "ProductionOrderNo": "SAP工单号", "MesOrderCode": "MES工单号", "SapFormula": "配方代码", "TextVersion": "PV", "Sequence": "序号", "PlanQty": "计划量", "Remark": "备注", "ThroatDetail": "喉头", "CipTypes": "CIP", "OrderType": "工单类型", "PoStatus": "工单状态", "SapFlag": "同步SAP状态", "LongTextResult": "长文本比对结果", "GeShai": "隔筛", "FangFuJi": "防腐剂", "PlanStartTime": "计划开始时间", "PlanEndTime": "计划结束时间"}, "Son_Work_Order_Info": {"Sequence": "序号", "date": "计划日期", "LineCode": "煮制线", "SegmentCode": "工作中心", "SapFormula": "配方代码", "TextVersion": "PV", "PlanQty": "计划量", "ThroatDetail": "喉头", "Remark": "备注"}, "Generate_Large_Table": {"a": "日期", "a1": "生产线", "a2": "规格", "a4": "配方", "a5": "物料编码", "a3": "配方名称", "a6": "计划表重量", "a7": "损耗", "a8": "喉头", "a9": "调节", "a10": "入轻", "a11": "排产重量", "a12": "缸数", "a13": "每缸数量", "a14": "总和", "a15": "备注"}, "Preliminary_Prod_Schedule": {"a": "日期", "a1": "生产线", "a2": "规格", "a4": "配方", "a5": "物料编码", "a3": "配方名称", "a6": "计划表重量", "a7": "损耗", "a8": "喉头", "a9": "调节", "a10": "入轻", "a11": "排产重量", "a12": "缸数", "a13": "每缸数量", "a14": "总和", "a15": "备注"}, "Loss_Selection": {"a": "日期", "a1": "生产线", "a2": "规格", "a6": "配方", "a7": "物料编码", "a5": "配方名称", "a8": "计划表重量"}, "Loss_Selection_Table": {"a9": "损耗方案选择"}, "Generate_Prod_Table": {"a": "日期", "a1": "生产线", "a2": "规格", "a4": "配方", "a5": "物料编码", "a3": "配方名称", "a6": "计划表重量", "a7": "损耗", "a8": "喉头", "a9": "调节", "a10": "入轻", "a11": "排产重量", "a12": "缸数", "a13": "每缸数量", "a14": "总和", "a15": "备注"}, "Edit_Prod_Table": {"a": "配方编号", "a2": "损耗", "a12": "调节", "a13": "入轻", "a14": "排产重量", "a15": "缸数", "a16": "每缸重量", "a17": "BOM版本"}, "Generate_Work_Order": {"a": "工单号码", "a2": "工单状态", "a12": "标准批量", "a13": "实际批量", "a15": "配方", "a14": "配方名称", "a16": "生产日期", "a17": "计划开始时间"}, "Edit_Work_Order": {"a": "工单号码", "a2": "工单状态", "a12": "标准批量", "a13": "实际批量", "a15": "配方", "a14": "配方名称", "a16": "生产日期", "a17": "计划开始时间"}, "Cancel_Work_Order": {"a": "工单号码", "a2": "工单状态", "a12": "标准批量", "a13": "实际批量", "a15": "配方", "a14": "配方名称", "a16": "生产日期", "a17": "计划开始时间"}, "Recipe_Sort": {"date": "日期", "LineCode": "产线名称", "PlanStartTime": "生产时间", "SapFormula": "配方代码", "MatCode": "配方编码", "MatName": "配方名称", "TextVersion": "配方版本"}, "Order_Search": {"e": "工单名称", "a": "工单号码", "b": "配方号码", "c": "生产开始时间", "d": "生产结束时间"}, "Public_Work_Order": {"a": "工单号码", "a2": "工单状态", "a12": "标准批量", "a13": "实际批量", "a14": "配方名称", "a15": "配方", "a16": "生产日期", "a17": "计划开始时间"}, "Robot_Motion": {"dataIndex": "序号", "LineCode": "产线", "Msg": "提示信息"}, "Create_Work_Order": {"a": "工单号码", "a2": "工单状态", "a12": "标准批量", "a13": "实际批量", "a14": "配方名称", "a15": "配方", "a16": "生产日期", "a17": "计划开始时间"}, "Formula_Throat_Detail": {"SapFormula": "配方", "ThroatCode": "酱料编码", "MatName": "酱料名称", "Throatformorder": "批号", "SSCC": "追溯码", "Inweight": "计划使用数量"}}, "scjxkpi": {"Comment": "备注", "ConfirmDate": "确认时间", "EventType": "安全事件类型", "MaterialBigGroup": "物料大分类", "MaterialSmallGroup": "物料小分类", "Target": "目标值", "Saucecategory": "酱料类别", "ChooseModel": "物理模型", "year": "年", "month": "月", "Name": "数据名称", "Type": "数据类型", "Value": "数值", "Unit": "单位", "Line": "生产线", "Spec": "规格", "SpecCode": "规格编码", "SpecName": "规格名称", "MaterialGroup": "原料群组", "SpecGroup": "规格群组", "BudgetType": "预算类型", "BudgetOutput": "预算产量", "Importtonnagebudget": "导入吨数预算", "Importboxquantitybudget": "导入箱数预算"}, "PackagingWorkOrder": {"title": {"packagingWorkOrder": "包装工单管理"}, "button": {"PackagingWorkOrder": "获取包装工单", "PackOrderSort": "自动排序", "OrderSort": "手动排序", "OrderSearch": "工单查询", "PublicWorkOrder": "发布工单", "EditAttribute": "备注", "RobotMotion": "节拍计算", "rework": "返工说明"}, "Table_Head": {"ChangeRecord": "变更履历", "ProDate": "计划日期", "SegmentCode": "工作中心", "SapStatus": "SAP状态", "PoStatus": "状态", "PackOrderNo": "工单号", "Formula": "配方号", "MatCode": "产品料号", "MatName": "产品名称", "PlanQty": "生产数量", "UnitCode": "单位", "SaleContainer": "包装规格", "Sequence": "序号", "Fgpri": "市场", "LTEXT1": "LTEXT1", "LTEXT2": "LTEXT2", "LTEXT3": "LTEXT3", "LTEXT4": "LTEXT4", "LTEXT5": "LTEXT5", "LTEXT6": "LTEXT6", "Zcylcd": "清缸代码", "Codtx": "清缸备注", "OrderJarClear": "PMC备注", "BomRemark": "物料转换备注", "Remark": "备注", "MAKTX_C_FW": "纸箱及招纸", "LANDZ": "销售市场", "IHRAN": "走柜日期", "VText": "成品规格", "PlanStartTime": "计划开始时间", "PlanEndTime": "计划结束时间", "MRP": "MRP控制者", "Speed": "包装速度", "WorkOrderType": "工单类型", "ResponsibleDepartment": "责任部门", "ReworkCategory": "返工类别", "ReasonForRework": "返工原因", "ReworkMethod": "返工方法"}, "Table_Head_Dialog": {"IsIgnore": "状态", "ProDate": "计划日期", "PackOrderNo": "工单号", "SaleContainer": "包装规格", "Formula": "配方号", "PsmngComp": "配方数量(KG)", "MatCode": "产品料号", "PlanQty": "生产数量", "UnitCode": "单位", "MngPuo": "总支数", "PreWeight": "单只重量", "Speed": "包装速度"}, "workOrderDetail": {"SegmentCode": "工作中心", "SapStatus": "SAP状态", "PoStatus": "状态", "ProDate": "计划生产日期", "Formula": "配方号", "MatName": "产品名称", "PackOrderNo": "工单号", "Sequence": "序号", "MatCode": "成品料号", "LTEXT1": "LTEXT1", "LTEXT2": "LTEXT2", "LTEXT3": "LTEXT3", "LTEXT4": "LTEXT4", "LTEXT5": "LTEXT5", "LTEXT6": "LTEXT6", "Zcylcd": "清缸代码", "Codtx": "清缸备注", "MAKTX_C_FW": "纸箱及招纸", "LANDZ": "销售市场", "IHRAN": "走柜日期", "SaleContainer": "包装规格", "VText": "成品规格", "PlanQty": "生产数量", "UnitCode": "单位", "Speed": "包装速度", "PlanStartTime": "计划开始时间", "PlanEndTime": "计划结束时间", "MRP": "MRP控制者", "WorkOrderType": "工单类型", "ResponsibleDepartment": "责任部门", "ReworkCategory": "返工类别", "ReasonForRework": "返工原因", "ReworkMethod": "返工方法"}, "Order_Sort": {"LineCode": "产线名称", "date": "日期", "PlanStartTime": "生产时间", "MatName": "工单名称", "PlanEndTime": "换型时间"}, "Public_Order": {"a": "日期", "b": "生产线", "c": "规格", "d": "配方名称", "e": "配方", "f": "物料编码", "g": "计划表重量", "h": "损耗"}, "work_order_bom": {"ItemDecs": "名称", "ItemId": "物料编码", "ItemQuantityWithLoss": "带损耗数量", "ItemQuantityWithoutLoss": "无损耗数量", "ItemUnit": "单位"}}, "CommunicationStation": {"title": {"communicationStation": "通讯站点管理", "type": "采集类型", "code": "站点编码", "name": "站点名称", "url": "URL"}, "Station_Table": {"Code": "站点编码", "Name": "站点名称", "Type": "采集类型", "Url": "URL", "Remarks": "备注"}}, "CollectionPoint": {"title": {"collectionPoint": "采集点管理", "station": "通讯站点", "type": "采集类型", "isSave": "是否储存"}, "Collection_Point_Table": {"Name": "点位名称", "Tag": "点位地址", "Describe": "点位描述", "ServerName": "通讯站点", "IsSave": "是否存储", "Type": "采集类型", "Formula": "计算公式"}}, "ConsoleGroup": {"title": {}, "table": {"ConsoleGroup": "分组", "SortOrder": "排序", "Description": "描述", "Type": "类型", "EquipmentNames": "设备"}, "EquipmentGroupEquip": {"SortOrder": "排序", "EquipmentName": "设备", "Type": "类型", "LineName": "产线"}}, "RecipeTable": {"table": {"Step": "步骤", "备注信息": "备注信息", "静置时间": "静置时间", "投料顺序号": "投料顺序号", "加水量": "加水量"}}, "Recipe": {"contentTile": "配方管理", "name": "名称", "code": "编码", "describe": "描述", "activation": "激活", "timeEffect": "生效时间", "startTime": "开始时间", "endTime": "结束时间", "opinion": "意见", "AllowSwitching": "配方参数表", "formSubmit": "确定", "close": "关闭", "recipeName": "配方名称", "ApprovalType": "审批类型", "Status": "状态", "Parameter": "参数名称", "type": "类型", "dataType": "数据类型", "accuracy": "精度", "MeasurementUnitId": "单位", "AutomaticCollection": "是否数采点位", "PointTag": "点位类型", "referredToAs": "简称", "QAReview": "QA审核", "AssociationType": "关联类型", "GenerateFrequency": "生成频率", "frequencyValue": "频率值", "remarks": "意见备注", "WoCode": "工单号"}, "sampling": {"WoCode": "工单号", "MaterialCode": "产品号", "Container": "容器", "Equipment": "设备"}, "MonthProductionDetail": {"table": {"Year": "年", "LineName": "生产线", "BeforeYearOee": "前年实际", "LastYearOee": "去年实际", "YearOee": "当前实际", "Type": "月", "RunTime": "总运行时间", "UnPlannedTime": "计划停机时间", "PlannedTime": "非计划停机时间", "StdProductionTime": "总产量标准时间", "DefectiveProducts": "不良品数", "TotalCount": "总支数", "Validity": "有效性%", "Expression": "表现性%", "Quality": "品质性%", "OEE": "OEE%", "MCU": "MCU%", "ACU": "ACU%"}}, "Throughput": {"table": {"a": "订单", "a1": "工作中心", "a2": "实际开始时间", "a3": "物料", "a4": "配方", "a5": "类别", "a6": "工厂", "a7": "单位", "a8": "目标数量", "a9": "实际数量"}}, "OneCompletion": {"table": {"Month": "月", "PlantDecs": "生产线", "PlanStartTime": "开始时间", "PlanEndTime": "完成时间", "ProductionOrderId": "订单", "MaterialId": "物料编码", "MaterialDecs": "物料说明", "MrpCode": "MRP控制号", "PlantId": "工厂", "PlanQtyBox": "订单数量", "ActualQtyBox": "已交货数量", "BoxUnit": "计量单位（箱）", "PlanQtyBottle": "订单数量（支）", "ActualQtyBottle": "已交货数量（支）", "BottleUnit": "计量单位（支）", "OneTimeCompletion": "完成数量", "ISCompletion": "是否完成", "Reason": "备注"}}, "NonStandardProducts": {"table": {"ReasonGroup": "原因大类", "ReasonList": "原因明细", "Hour": "工时", "QtyBox": "产量箱", "QtyBottle": "产量支"}}, "ReworkWorkOrder": {"table": {"ReworkorderCreatedate": "建单日期", "Month": "返工完成月份", "LineId": "生产线", "ProductionOrderId": "工单号", "MaterialId": "产品编码", "MaterialDecs": "产品名称", "ReworkorderBatchId": "批号", "ReworkorderReworkqty": "数量", "ReworkorderUnit": "单位", "ReworkorderAuthority": "责任部门", "ReworkorderType": "返工类别", "ReworkorderReason": "返工原因", "ReworkorderMethod": "返工方法", "ActualQtyBottle": "返工后数量", "LaborHour": "所用人时", "MachHour": "所用工时", "CostCenter": "成本中心", "HourType1": "单位(机时)", "ReworkorderRemark": "备注"}}, "CookingTankAdjustment": {"table": {"QASum": "检验总缸数", "AdjustmentNum": "调节缸数", "AdjustmentRate": "调节率(%)", "ReleasedNum": "放行缸数", "ReleaseRate": "放行率(%)", "ReworkNum": "返缸数", "AntiCylinderRate": "返缸率(%)"}}, "PackagingLineEquipment": {"table": {"LineName": "生产线", "Target": "目标", "RunningTime": "行车时间", "TotalOccupationTime": "运行时间", "TotalDowntime": "总停机时间(min)", "DowntimeAffactProducion": "停机时间(min)", "TotalDowntimeRate": "总停机率%", "AffactDowntimeRate": "影响生产停机率%"}}, "ReportOee": {"table": {"a": "总运行时间", "a1": "计划停机时间", "a2": "非计划停机时间", "a3": "总产量标准时间", "a4": "有效性", "a5": "表现性", "a6": "OEE"}}, "ShutdownTimeAnalysis": {"table": {"GroupName": "停机原因", "Time1": "二类时间1", "Time2": "二类时间2", "ReasonLists": "停机原因三类List", "TotalTime1": "总计时间1", "TotalTime2": "总计时间2"}}, "MaterialLabelTable": {"GetLotNumber": "获取批次号"}, "report": {"MonthProductionDetail": {"Year": "年", "Month": "月", "LineId": "生产线", "StartTime": "开始时间", "ProductionOrderId": "订单编号", "MaterialId": "物料编号", "MaterialDecs": "物料说明", "Formula": "配方", "FormulaType": "酱料类别", "SalesContainer": "规格", "SalesContainerGroup": "规格类别", "MrpDesc": "内/外销", "ActualQtyBox": "箱数", "ActualQtyBottle": "支数", "ActualNetWeightKg": "净重", "ActualQtyKg": "重量", "ActualQtyTon": "吨数", "LaborHour": "人工工时", "MachHour": "机时工时", "WorkCenter": "工作中心", "MrpCode": "MRP", "FormulaGroup": "酱料系列", "PlatesNum": "板数", "BoxPerPlate": "每箱板数"}, "Throughput": {"ProductionOrderId": "订单", "WorkCenter": "工作中心", "StartTime": "实际开始时间", "MaterialId": "物料编码", "Formula": "配方编码", "MaterialDecs": "物料描述", "FormulaType": "酱料类别", "PlantDecs": "工厂名称", "KgUnit": "单位", "PlanQtyTon": "计划生产重量", "ActualQtyKg": "实际产出重量", "ActualConsumeQtyKg": "实际投入重量", "Rate": "产出率 Rate %", "FW": "配方+工作中心", "LastYield": "上年平均产出率", "Difference": "差异", "LineId": "生产线"}}, "PackSpeed": {"table": {"WorkCenter": "工作中心", "SalescontainerCode": "规格", "SalescontainerName": "规格描述", "Packingunit": "包装单位", "PackingunitName": "包装单位描述", "ProductionCategory": "产品类别", "CustomerType": "客户类别", "FormulaCode": "配方代码", "Mrp": "MRP", "Remark": "瓶盖关键字", "Speed": "生产速度", "OeeSpeed": "OEE速度"}}, "WeekSchedule": {"table": {"Workshop": "车间", "LineCode": "产线代码", "Category": "分类", "MaterialCode": "物料代码", "MaterialName": "物料描述", "Factory": "工厂", "PackSize": "包装规格", "DesignCode": "设计代码", "Unit": "单位", "Output": "输出", "StartWorkday": "开始工作日", "StartShift": "开始班次", "FinishWorkday": "完成工作日", "FinishShift": "完成班次", "PlanQuantity": "计划数量", "ReworkMaterialCode": "返工物料代码", "RreworkMaterialName": "返工物料描述", "ReworkQuantity": "返工用料数量", "OrderNo": "计划编号", "Type": "计划类型", "Status": "状态", "SapOrderNo": "SAP工单号", "SapStatus": "SAP状态", "SapFeedbackQuantity": "SAP报工数量", "WorkCenter": "工作中心", "ActualStartWorkday": "实际开始工作日", "ActualStartTime": "实际开始时间", "ActualStartShift": "实际开始班次", "ActualFinishWorkday": "时间完成工作日", "ActualFinishTime": "实际完成时间", "ActualFinishShift": "实际完成时间", "ActualQuantity": "实际产出数量", "Remark": "备注"}, "bomDetail": {"SegmentName": "工序名", "Sort": "顺序", "MaterialCode": "物料代码", "MaterialName": "物料名称", "MaterialType": "物料类型", "InsteadMaterialCode": "替代物料代码", "InsteadMaterialName": "替代物料名称", "Unit": "单位", "StandardQuantity": "标准批次量", "PlanQuantity": "计划数量", "Remark": "备注"}}, "WeekFormulation": {"table": {"OrderNo": "生产计划号", "Type": "类型", "LineCode": "产线", "Factory": "工厂", "MaterialCode": "产品代码", "MaterialName": "产品名称", "StartWorkday": "计划开始时间", "FinishWorkday": "计划完成时间", "PlanQuantity": "计划数量", "Status": "状态", "SapFeedbackQuantity": "报工数量"}, "bomDetail": {"SegmentName": "工序名", "Sort": "顺序", "MaterialCode": "物料代码", "MaterialName": "物料名称", "MaterialType": "物料类型", "InsteadMaterialCode": "替代物料代码", "InsteadMaterialName": "替代物料名称", "Unit": "单位", "StandardQuantity": "标准批次量", "PlanQuantity": "计划数量", "Remark": "备注"}, "poListDetail": {"Sequence": "顺序号", "ProductionOrderNo": "工单号", "MaterialCode": "产品代码", "MaterialName": "产品名称", "PlanQty": "计划数量", "PlanDate": "计划日", "PrepareShiftid": "班次", "PlanStartTime": "计划开始时间", "PlanEndTime": "计划结束时间", "StartTime": "开始时间", "EndTime": "结束时间", "PoStatus": "Po状态", "ReleaseStatus": "Release状态", "ProduceStatus": "生产状态", "Type": "类型", "BomVersion": "BOM版本", "SegmentCode": "工序代码", "LineCode": "产线代码", "Remark": "备注"}}, "BatchDcs": {"table": {"LineName": "产线", "PoNo": "生产工单", "BatchtNo": "批次号", "MaterialCode": "物料号", "MaterialName": "物料名称", "Unit": "单位", "StandardQuantity": "标准批次量", "PlanQuantity": "计划数量", "Status": "状态", "SendData": "发送数据", "ResponseData": "返回数据", "Remark": "备注", "CreateDate": "创建时间", "CreateUserId": "创建人", "ModifyDate": "修改时间", "ModifyUserId": "修改人"}}, "StandardPeriodLot": {"table": {"LineCode": "产线", "MaterialCode": "物料代码", "MaterialName": "物料名称", "Type": "类型", "FirstLotPeriod": "第一批时间", "MiddleLotPeriod": "中间批时间", "LastLotPeriod": "最后一批时间", "PlanQuantity": "计划数量", "MinLotQuantity": "最小成批数", "MaxLotQuantity": "最大成批数", "Remark": "备注", "Createdate": "创建时间", "Createuserid": "创建人", "Modifydate": "修改时间", "Modifyuserid": "修改人"}}, "FormulaTankCapacity": {"table": {"LineName": "产线", "FormulaCode": "配方", "MaterialCode": "物料编号", "MaterialName": "物料名称", "TankCapacity": "煮缸容量", "MinWeight": "最小重量", "MaxWeight": "最大重量"}}, "CIPSetting": {"table": {"Switchname": "CIP类型", "IsBefore": "生产前清洗", "Remark": "备注"}}, "CookTimeModel": {"table": {"MaterialName": "酱料名称", "FormulaCode": "配方号", "StandardWeight": "煮罐重量", "StandardHours": "生产耗时", "Duration": "标准泵料时长"}}, "CIPInfo": {"table": {"EquipmentCode": "设备编码", "OrderCode": "MES工单号", "CipOrderCode": "CIP工单号", "CipType": "CIP方式", "CipStation": "CIP工作站", "CipStart": "CIP开始时间", "CipEnd": "CIP结束时间", "CheckResult": "审核结果", "CheckUserName": "审核人姓名", "CheckTime": "审核时间", "Remark": "备注"}}, "MMILog": {"table": {"OrderNo": "工单号", "BatchNo": "批号", "CreateDate": "创建时间", "Name": "接口名", "Description": "接口描述", "Content": "日志内容", "Detail": "明细"}}, "EnteringLightweight": {"table": {"FormulaCode": "配方号", "MaterialCode": "酱料编号", "MaterialName": "酱料名称", "SaleContainerCode": "规格代码", "SaleContainerName": "规格名称", "Weight": "入轻重量", "Remark": "备注"}}, "MaterialRequirement": {"table": {"MaterialCode": "物料编号", "MaterialName": "物料名称", "MaterialType": "物料分类", "WeekDay1": "周一", "WeekDay2": "周二", "WeekDay3": "周三", "WeekDay4": "周四", "WeekDay5": "周五", "WeekDay6": "周六", "WeekDay7": "周日", "Total": "汇总", "Unit": "单位"}}, "MaterialSchedulingAttribute": {"table": {"SapFormula": "配方", "MaterialCode": "配方编号", "MaterialName": "配方名称", "HighPriority": "高优先级生成", "HaveOil": "是否含油", "IsSmooth": "是否磨滑", "HaveMaifu": "是否含麦麸", "HavePreservative": "是否含防腐剂", "HaveColor": "是否含色素", "Sievesize": "隔筛"}}, "SOP": {"DirName": "文件名称", "DirCode": "文件编号", "DocName": "文档名称", "DocCode": "文档编码", "DocVersion": "文档版本", "FilePath": "文件路径", "FileSize": "文件大小", "DocStatus": "状态", "OperationType": "操作类型", "OperatorId": "操作人", "OperateTime": "操作时间", "AuditResult": "审核状态", "AuditComment": "审核意见", "Preview": "预览", "Approve": "通过", "Reject": "不通过", "RejectReason": "不通过原因", "EnterRejectReason": "请输入审核不通过的原因", "RejectReasonRequired": "请输入不通过原因", "ConfirmApprove": "确认通过此审核吗？", "ApproveSuccess": "审核通过成功", "RejectSuccess": "审核不通过处理成功", "PreviewNotImplemented": "预览功能待实现", "StatusValid": "有效", "StatusInvalid": "无效", "OperationCreate": "创建", "OperationModify": "修改", "OperationDelete": "删除", "AuditPending": "待审核", "AuditInProgress": "审核中", "AuditApproved": "审核通过", "AuditRejected": "审核不通过", "AuditPassed": "审核通过"}, "SopDoc": {"table": {"DocName": "文档名称", "DocCode": "文档编码", "DocVersion": "文档版本", "FilePath": "文件路径", "FileSize": "文件大小", "DocStatus": "状态", "CreateTime": "创建时间", "CreateUserId": "创建人ID", "ModifyTime": "修改时间", "ModifyUserId": "修改人ID"}}, "SopAudit": {"table": {"DocName": "文档名称", "DocCode": "文档编码", "DocVersion": "文档版本", "FilePath": "文件路径", "FileSize": "文件大小", "DocStatus": "状态", "OperationType": "操作类型", "OperatorId": "操作人", "OperateTime": "操作时间", "AuditResult": "审核状态", "AuditComment": "审核意见", "CreateTime": "创建时间", "CreateUserId": "创建人ID", "ModifyTime": "修改时间", "ModifyUserId": "修改人ID"}}, "SopPermission": {"table": {"RoleName": "角色名称", "Upload": "上传", "Download": "下载", "Preview": "预览", "Search": "检索", "Delete": "删除", "Operation": "操作", "SelectAll": "全选", "CancelAll": "取消全选", "TargetId": "授权对象ID", "TargetType": "对象类型", "GrantType": "授权类型", "GrantId": "被授权对象ID", "PermLevel": "权限级别", "CreateTime": "创建时间", "CreateUserId": "创建人ID", "ModifyTime": "修改时间", "ModifyUserId": "修改人ID"}}}