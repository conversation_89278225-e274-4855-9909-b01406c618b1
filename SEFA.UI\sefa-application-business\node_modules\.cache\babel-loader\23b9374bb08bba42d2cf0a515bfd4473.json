{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopAudit\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopAudit\\index.vue", "mtime": 1750249305291}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\babel.config.js", "mtime": 1742799047338}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AA0HA;AAEA;AACA,SACAA,WADA,EACAC,eADA,EACAC,gBADA,QAEA,oBAFA;AAIA;AAGA;EACAC,iBADA;EAEAC;IACAC;EADA,CAFA;;EAKAC;IACA;MACAC;QACAC,YADA;QAEAC;MAFA,CADA;MAKAC,QALA;MAMAC,aANA;MAOAC,yBAPA;MAQAC,cARA;MASAC,QATA;MAUAC;QACAZ,aADA;QAEAa,sBAFA;QAGAC,qCAHA;QAGA;QACAC,qCAJA;QAIA;QACAC,6CALA,CAKA;;MALA,CAVA;MAiBA;MACAC,0BAlBA;MAmBAC;QACAC;MADA,CAnBA;MAsBAC;IAtBA;EAwBA,CA9BA;;EA+BAC;IACA;IACA;MACA;IACA,CAFA;;IAGAC;MACA;IACA,CAFA;EAGA,CAvCA;;EAwCAC;IACAC;MACA;IACA,CAHA;;IAIAC;MACA;MACA;IACA,CAPA;;IAQAC;MACA;MACA;IACA,CAXA;;IAYAC;MACA;MACA;IACA,CAfA;;IAiBAC;MACA;QACAC,4BADA;QAEAC,mCAFA;QAGAC,kCAHA;QAIAC;MAJA,GAKAC,IALA,CAKA;QACApC;UACA;UACA;QACA,CAHA;MAIA,CAVA,EAUAqC,KAVA,CAUAC;QACAC;MACA,CAZA;IAaA,CA/BA;;IAiCAC;MACAvC;QACA;QACA;MACA,CAHA;IAIA,CAtCA;;IAwCA;IACAwC;MACA;MACA;IACA,CA5CA;;IA8CA;IACAC;MACA;QACAC,wCADA;QAEAC,uCAFA;QAGAC;MAHA,GAIAT,IAJA,CAIA;QACA,oBACA,MADA;UAEAU,cAFA;UAEA;UACAC,qCAHA;UAIAC;QAJA;QAMA9C;UACA;UACA;QACA,CAHA;MAIA,CAfA,EAeAmC,KAfA,CAeA;QACA;MACA,CAjBA;IAkBA,CAlEA;;IAoEA;IACAY;MACA;MACA;MACA;IACA,CAzEA;;IA2EA;IACAC;MACA;QACA;UACA,oBACA,uBADA;YAEAJ,cAFA;YAEA;YACAC,qCAHA;YAIAC;UAJA;UAMA9C;YACA;YACA;YACA;UACA,CAJA;QAKA;MACA,CAdA;IAeA,CA5FA;;IA8FA;IACAiD;MACA;MACA;MACA;;MACA;QACAC;QACAC;MACA;;MACA;IACA,CAxGA;;IA0GA;IACAC;MACA;QACA,6BADA;QAEA;MAFA;MAIA;IACA,CAjHA;;IAmHA;IACAC;MACA;IACA,CAtHA;;IAwHA;IACAC;MACA;QACA,iCADA;QAEA,iCAFA;QAGA;MAHA;MAKA;IACA,CAhIA;;IAkIA;IACAC;MACA;QACA,8BADA;QAEA,iCAFA;QAGA,+BAHA;QAIA;MAJA;MAMA;IACA,CA3IA;;IA6IA;IACAC;MACA;QACA,YADA;QAEA,YAFA;QAGA,YAHA;QAIA;MAJA;MAMA;IACA;;EAtJA;AAxCA,E,CAkMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "names": ["delSopAudit", "getSopAuditList", "saveSopAuditForm", "name", "components", "FormDialog", "data", "searchForm", "pageIndex", "pageSize", "total", "tableData", "tableName", "loading", "mainH", "buttonOption", "serveIp", "uploadUrl", "exportUrl", "DownLoadUrl", "rejectDialogVisible", "rejectForm", "auditComment", "currentAuditRow", "mounted", "window", "methods", "showDialog", "handleCurrentChange", "handleSizeChange", "getSearchBtn", "delRow", "title", "message", "confirmText", "cancelText", "then", "catch", "err", "console", "getTableData", "previewDoc", "<PERSON><PERSON><PERSON><PERSON>", "confirmButtonText", "cancelButtonText", "type", "AuditResult", "AuditUserId", "AuditComment", "<PERSON><PERSON><PERSON><PERSON>", "confirmReject", "formatFileSize", "size", "index", "formatStatus", "getStatusType", "formatOperationType", "formatAuditResult", "getAuditResultType"], "sourceRoot": "src/views/SOP/sopAudit", "sources": ["index.vue"], "sourcesContent": ["<template>\n  <div class=\"root\">\n    <div class=\"root-head\">\n      <el-form size=\"small\" :inline=\"true\" ref=\"form\" :model=\"searchForm\" @submit.native.prevent>\n            \t\t\t\t    \n      <el-form-item label=\"关联文档ID\" prop=\"docId\">\n        <el-select v-model=\"searchForm.docId\" placeholder=\"请选择关联文档ID\">\n          <el-option v-for=\"item in  docIdOptions\" :key=\"item.dictValue\" :label=\"item.dictLabel\" :value=\"item.dictValue\"></el-option>\n        </el-select>\n      </el-form-item>\n    \t\t\t\t    \n      <el-form-item label=\"操作类型(1-创建 2-修改 3-删除)\" prop=\"operationType\">\n        <el-select v-model=\"searchForm.operationType\" placeholder=\"请选择操作类型(1-创建 2-修改 3-删除)\">\n          <el-option v-for=\"item in  operationTypeOptions\" :key=\"item.dictValue\" :label=\"item.dictLabel\" :value=\"item.dictValue\"></el-option>\n        </el-select>\n      </el-form-item>\n    \t\t\t\t    \n      <el-form-item label=\"操作人ID\" prop=\"operatorId\">\n        <el-select v-model=\"searchForm.operatorId\" placeholder=\"请选择操作人ID\">\n          <el-option v-for=\"item in  operatorIdOptions\" :key=\"item.dictValue\" :label=\"item.dictLabel\" :value=\"item.dictValue\"></el-option>\n        </el-select>\n      </el-form-item>\n\n        <el-form-item class=\"mb-2\">\n          <el-button icon=\"el-icon-search\" @click=\"getSearchBtn()\">{{ $t('GLOBAL._CX') }}</el-button>\n        </el-form-item>\n        <el-form-item>\n          <el-button size=\"small\" type=\"success\" icon=\"el-icon-circle-plus-outline\" @click=\"showDialog({})\">\n            {{ $t('GLOBAL._XZ') }}\n          </el-button>\n        </el-form-item>\n              </el-form>\n    </div>\n    <div class=\"root-main\">\n      <el-table class=\"mt-3\"\n                :height=\"mainH\"\n                border\n                :data=\"tableData\"\n                style=\"width: 100%\">\n        <el-table-column\n          type=\"index\"\n          label=\"序号\"\n          width=\"50\"\n          align=\"center\">\n        </el-table-column>\n        <el-table-column v-for=\"(item) in tableName\"\n                         :default-sort=\"{prop: 'date', order: 'descending'}\"\n                         :key=\"item.value\"\n                         :prop=\"item.value\"\n                         :label=\"typeof item.text === 'function' ? item.text() : item.text\"\n                         :width=\"item.width\"\n                         :align=\"item.alignType || 'center'\"\n                         sortable\n                         show-overflow-tooltip\n        >\n          <template slot-scope=\"scope\">\n            <template v-if=\"item.value === 'FileSize'\">\n              {{ formatFileSize(scope.row[item.value]) }}\n            </template>\n            <template v-else-if=\"item.value === 'DocStatus'\">\n              <el-tag :type=\"getStatusType(scope.row[item.value])\" size=\"small\">\n                {{ formatStatus(scope.row[item.value]) }}\n              </el-tag>\n            </template>\n            <template v-else-if=\"item.value === 'OperationType'\">\n              {{ formatOperationType(scope.row[item.value]) }}\n            </template>\n            <template v-else-if=\"item.value === 'AuditResult'\">\n              <el-tag :type=\"getAuditResultType(scope.row[item.value])\" size=\"small\">\n                {{ formatAuditResult(scope.row[item.value]) }}\n              </el-tag>\n            </template>\n            <template v-else>\n              {{ scope.row[item.value] }}\n            </template>\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"operation\" width=\"200\" :label=\"$t('GLOBAL._ACTIONS')\" align=\"center\">\n          <template slot-scope=\"scope\">\n            <el-button size=\"mini\" type=\"primary\" @click=\"previewDoc(scope.row)\">{{ $t('SOP.Preview') }}</el-button>\n            <el-button size=\"mini\" type=\"success\" @click=\"approveAudit(scope.row)\"\n                       :disabled=\"scope.row.AuditResult !== null && scope.row.AuditResult !== 0\">{{ $t('SOP.Approve') }}</el-button>\n            <el-button size=\"mini\" type=\"danger\" @click=\"rejectAudit(scope.row)\"\n                       :disabled=\"scope.row.AuditResult !== null && scope.row.AuditResult !== 0\">{{ $t('SOP.Reject') }}</el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n    </div>\n    <div class=\"root-footer\">\n      <el-pagination\n          class=\"mt-3\"\n          @size-change=\"handleSizeChange\"\n          @current-change=\"handleCurrentChange\"\n          :current-page=\"searchForm.pageIndex\"\n          :page-sizes=\"[10,20, 50, 100,500]\"\n          :page-size=\"searchForm.pageSize\"\n          layout=\"->,total, sizes, prev, pager, next, jumper\"\n          :total=\"total\"\n          background\n      ></el-pagination>\n    </div>\n    <form-dialog @saveForm=\"getSearchBtn\" ref=\"formDialog\"></form-dialog>\n\n    <!-- 审核不通过原因对话框 -->\n    <el-dialog :title=\"$t('SOP.RejectReason')\" :visible.sync=\"rejectDialogVisible\" width=\"500px\"\n               :close-on-click-modal=\"false\" :close-on-press-escape=\"false\">\n      <el-form ref=\"rejectForm\" :model=\"rejectForm\" label-width=\"120px\">\n        <el-form-item :label=\"$t('SOP.RejectReason')\" prop=\"auditComment\"\n                      :rules=\"[{ required: true, message: $t('SOP.RejectReasonRequired'), trigger: 'blur' }]\">\n          <el-input type=\"textarea\" v-model=\"rejectForm.auditComment\"\n                    :placeholder=\"$t('SOP.EnterRejectReason')\" :rows=\"4\"></el-input>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"rejectDialogVisible = false\">{{ $t('GLOBAL._QX') }}</el-button>\n        <el-button type=\"primary\" @click=\"confirmReject\">{{ $t('GLOBAL._QD') }}</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport '@/views/Inventory/mystyle.scss';\n\nimport FormDialog from './form-dialog'\nimport {\n    delSopAudit, getSopAuditList, saveSopAuditForm\n} from \"@/api/SOP/sopAudit\";\n\nimport { sopAuditColumn } from '@/api/SOP/sopAudit.js';\n\n\nexport default {\n  name: 'index.vue',\n  components: {\n    FormDialog,\n  },\n  data() {\n    return {\n      searchForm: {\n        pageIndex: 1,\n        pageSize: 20,\n      },\n      total: 0,\n      tableData: [],\n      tableName: sopAuditColumn,\n      loading: false,\n      mainH: 0,\n      buttonOption:{\n        name:'SOP审核',\n        serveIp:'baseURL_DFM',\n        uploadUrl:'/api/SopAudit/ImportData', //导入\n        exportUrl:'/api/SopAudit/ExportData', //导出\n        DownLoadUrl:'/api/SopAudit/DownLoadTemplate', //下载模板\n      },\n      // 审核不通过原因对话框\n      rejectDialogVisible: false,\n      rejectForm: {\n        auditComment: ''\n      },\n      currentAuditRow: null\n    }\n  },\n  mounted() {\n    this.getTableData()\n    this.$nextTick(() => {\n      this.mainH = this.$webHeight(document.getElementsByClassName('root-head')[0].clientHeight, document.getElementsByClassName('root')[0].clientHeight)\n    })\n    window.onresize = () => {\n      this.mainH = this.$webHeight(document.getElementsByClassName('root-head')[0].clientHeight, document.getElementsByClassName('root')[0].clientHeight)\n    }\n  },\n  methods: {\n    showDialog(row) {\n      this.$refs.formDialog.show(row)\n    },\n    handleCurrentChange(page) {\n      this.searchForm.pageIndex = page\n      this.getTableData()\n    },\n    handleSizeChange(size) {\n      this.searchForm.pageSize = size\n      this.getTableData()\n    },\n    getSearchBtn() {\n      this.searchForm.pageIndex = 1\n      this.getTableData()\n    },\n\n    delRow(row) {\n      this.$confirms({\n        title: this.$t('GLOBAL._TS'),\n        message: this.$t('GLOBAL._COMFIRM'),\n        confirmText: this.$t('GLOBAL._QD'),\n        cancelText: this.$t('GLOBAL._QX')\n      }).then(async () => {\n        delSopAudit([row.ID]).then(res => {\n          this.$message.success(res.msg)\n          this.getTableData()\n        })\n      }).catch(err => {\n        console.log(err);\n      });\n    },\n\n    getTableData() {\n      getSopAuditList(this.searchForm).then(res => {\n        this.tableData = res.response.data\n        this.total = res.response.dataCount\n      })\n    },\n\n    // 预览文档\n    previewDoc(row) {\n      // 这里可以实现文档预览功能\n      this.$message.info(this.$t('SOP.PreviewNotImplemented'))\n    },\n\n    // 通过审核\n    approveAudit(row) {\n      this.$confirm(this.$t('SOP.ConfirmApprove'), this.$t('GLOBAL._TS'), {\n        confirmButtonText: this.$t('GLOBAL._QD'),\n        cancelButtonText: this.$t('GLOBAL._QX'),\n        type: 'warning'\n      }).then(() => {\n        const auditData = {\n          ...row,\n          AuditResult: 2, // 审批通过\n          AuditUserId: this.$store.getters.name,\n          AuditComment: this.$t('SOP.AuditPassed')\n        }\n        saveSopAuditForm(auditData).then(res => {\n          this.$message.success(this.$t('SOP.ApproveSuccess'))\n          this.getTableData()\n        })\n      }).catch(() => {\n        this.$message.info(this.$t('GLOBAL._QXCZ'))\n      })\n    },\n\n    // 不通过审核\n    rejectAudit(row) {\n      this.currentAuditRow = row\n      this.rejectForm.auditComment = ''\n      this.rejectDialogVisible = true\n    },\n\n    // 确认不通过\n    confirmReject() {\n      this.$refs.rejectForm.validate((valid) => {\n        if (valid) {\n          const auditData = {\n            ...this.currentAuditRow,\n            AuditResult: 3, // 审批不通过\n            AuditUserId: this.$store.getters.name,\n            AuditComment: this.rejectForm.auditComment\n          }\n          saveSopAuditForm(auditData).then(res => {\n            this.$message.success(this.$t('SOP.RejectSuccess'))\n            this.rejectDialogVisible = false\n            this.getTableData()\n          })\n        }\n      })\n    },\n\n    // 格式化文件大小\n    formatFileSize(size) {\n      if (!size) return ''\n      const units = ['B', 'KB', 'MB', 'GB']\n      let index = 0\n      while (size >= 1024 && index < units.length - 1) {\n        size /= 1024\n        index++\n      }\n      return `${size.toFixed(2)} ${units[index]}`\n    },\n\n    // 格式化文档状态\n    formatStatus(status) {\n      const statusMap = {\n        1: this.$t('SOP.StatusValid'),\n        0: this.$t('SOP.StatusInvalid')\n      }\n      return statusMap[status] || this.$t('GLOBAL._WZ')\n    },\n\n    // 获取状态类型\n    getStatusType(status) {\n      return status === 1 ? 'success' : 'danger'\n    },\n\n    // 格式化操作类型\n    formatOperationType(type) {\n      const typeMap = {\n        1: this.$t('SOP.OperationCreate'),\n        2: this.$t('SOP.OperationModify'),\n        3: this.$t('SOP.OperationDelete')\n      }\n      return typeMap[type] || this.$t('GLOBAL._WZ')\n    },\n\n    // 格式化审核结果\n    formatAuditResult(result) {\n      const resultMap = {\n        0: this.$t('SOP.AuditPending'),\n        1: this.$t('SOP.AuditInProgress'),\n        2: this.$t('SOP.AuditApproved'),\n        3: this.$t('SOP.AuditRejected')\n      }\n      return resultMap[result] || this.$t('GLOBAL._WZ')\n    },\n\n    // 获取审核结果类型\n    getAuditResultType(result) {\n      const typeMap = {\n        0: 'warning',\n        1: 'primary',\n        2: 'success',\n        3: 'danger'\n      }\n      return typeMap[result] || 'info'\n    }\n  }\n}\n\n//<!-- 移到到src/local/en.json和zh-Hans.json -->\n//\"SopAudit\": {\n//    \"table\": {\n//        \"docId\": \"docId\",\n//        \"operationType\": \"operationType\",\n//        \"oldValue\": \"oldValue\",\n//        \"newValue\": \"newValue\",\n//        \"operatorId\": \"operatorId\",\n//        \"operateTime\": \"operateTime\",\n//        \"clientIp\": \"clientIp\",\n//        \"createdate\": \"createdate\",\n//        \"createuserid\": \"createuserid\",\n//        \"modifydate\": \"modifydate\",\n//        \"modifyuserid\": \"modifyuserid\",\n//        \"deleted\": \"deleted\",\n//    }\n//},\n</script>\n\n<style lang=\"scss\" scoped>\n.el-form-item--small.el-form-item {\n  margin-bottom: 0px;\n}\n\n.mt-8p {\n  margin-top: 8px;\n}\n\n.pd-left {\n  padding-left: 5px\n}\n</style>"]}]}