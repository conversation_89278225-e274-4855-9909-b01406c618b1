<template>
  <div class="root usemystyle">
    <div class="root-layout" v-loading="initLoading">
      <div class="root-content">
        <div class="InventorySearchBox">
          <div class="search-form">
            <el-form size="small" :inline="true" ref="form" :model="searchForm" @submit.native.prevent>
              <div class="form-content">
                <div class="search-area">
                  <div class="search-row">
                    <el-form-item :label="$t('SOP.DocName')" prop="docName" label-width="60px">
                      <el-input v-model="searchForm.docName" :placeholder="$t('SOP.EnterDocName')" clearable size="small" style="width: 180px;">
                      </el-input>
                    </el-form-item>
                    <el-form-item :label="$t('SOP.DocCode')" prop="docCode" label-width="60px">
                      <el-input v-model="searchForm.docCode" :placeholder="$t('SOP.EnterDocCode')" clearable size="small" style="width: 120px;"></el-input>
                    </el-form-item>
                    <el-form-item :label="$t('SOP.DocVersion')" prop="docVersion" label-width="60px">
                      <el-input v-model="searchForm.docVersion" placeholder="输入版本" clearable size="small" style="width: 80px;"></el-input>
                    </el-form-item>
                    <el-form-item :label="$t('SOP.DocStatus')" prop="docStatus" label-width="40px">
                      <el-select v-model="searchForm.docStatus" placeholder="选择" clearable size="small" style="width: 70px;">
                        <el-option :label="$t('SOP.StatusValid')" :value="1"></el-option>
                        <el-option :label="$t('SOP.StatusInvalid')" :value="0"></el-option>
                      </el-select>
                    </el-form-item>
                    <div class="action-buttons">
                      <el-button type="primary" icon="el-icon-search" @click="getSearchBtn()" size="small">{{ $t('GLOBAL._CX') }}</el-button>
                      <el-button size="small" icon="el-icon-refresh" @click="resetForm">{{ $t('GLOBAL._CZ') }}</el-button>
                    </div>
                  </div>
                </div>
              </div>
            </el-form>
          </div>
        </div>
        <div class="root-main">
          <el-table class="mt-3"
                    :height="700"
                    border
                    v-loading="tableLoading"
                    :data="tableData"
                    style="width: 100%; border-radius: 4px;"
                    :empty-text="'暂无数据'">
            <el-table-column
              type="index"
              label="序号"
              width="50"
              align="center">
            </el-table-column>
            <el-table-column v-for="(item) in tableName"
                             :default-sort="{prop: item.value, order: 'descending'}"
                             :key="item.value"
                             :prop="item.value"
                             :label="typeof item.text === 'function' ? item.text() : item.text"
                             :width="item.width"
                             :align="item.alignType || 'center'"
                             sortable
                             show-overflow-tooltip>
              <template slot-scope="scope">
                <template v-if="item.value === 'FileSize'">
                  {{ formatFileSize(scope.row[item.value]) }}
                </template>
                <template v-else-if="item.value === 'DocStatus'">
                  <el-tag :type="getStatusType(scope.row[item.value])" size="small">
                    {{ formatStatus(scope.row[item.value]) }}
                  </el-tag>
                </template>
                <template v-else>
                  {{ scope.row[item.value] }}
                </template>
              </template>
            </el-table-column>
            <el-table-column prop="operation" :min-width="120" :label="$t('GLOBAL._ACTIONS')" align="center">
              <template slot-scope="scope">
                <el-button size="mini" type="text" @click="handleDownload(scope.row)">{{ $t('SOP.Preview') }}</el-button>
                <el-button size="mini" type="text" @click="handleDownload(scope.row)">{{ $t('GLOBAL.Download') }}</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="root-footer">
          <el-pagination
              class="mt-3"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="searchForm.pageIndex"
              :page-sizes="[10,20, 50, 100,500]"
              :page-size="searchForm.pageSize"
              layout="->,total, sizes, prev, pager, next, jumper"
              :total="total"
              background
          ></el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import '@/views/Inventory/mystyle.scss'
import {
    getSopDocList, downloadSopDoc
} from "@/api/SOP/sopDoc";
import { sopDocColumns } from '@/columns/SOP/sopDoc.js';

export default {
  name: 'index.vue',
  components: {
  },
  data() {
    return {
      searchForm: {
        pageIndex: 1,
        pageSize: 20,
        docName: '',
        docCode: '',
        docVersion: '',
        docStatus: ''
      },
      total: 0,
      tableData: [],
      tableName: sopDocColumns,
      tableLoading: false,
      initLoading: false
    }
  },
  async mounted() {
    try {
      this.initLoading = true
      await this.getTableData()
    } catch (err) {
      console.error('页面初始化失败:', err)
      this.$message.error('页面初始化失败，请刷新重试')
    } finally {
      this.initLoading = false
    }
  },
  beforeDestroy() {
    window.onresize = null
  },
  methods: {
    // 格式化文件大小
    formatFileSize(size) {
      if (!size) return '0 B'
      const units = ['B', 'KB', 'MB', 'GB', 'TB']
      let index = 0
      let fileSize = parseFloat(size)
      while (fileSize >= 1024 && index < units.length - 1) {
        fileSize /= 1024
        index++
      }
      return `${fileSize.toFixed(2)} ${units[index]}`
    },

    // 获取状态对应的类型
    getStatusType(status) {
      switch (status) {
        case 1: return 'success'
        case 2: return 'warning'
        case 0: return 'info'
        default: return ''
      }
    },

    // 格式化状态
    formatStatus(status) {
      switch (status) {
        case 1: return this.$t('SOP.StatusValid')
        case 2: return this.$t('SOP.AuditPending')
        case 0: return this.$t('SOP.StatusInvalid')
        default: return this.$t('GLOBAL._WZ')
      }
    },

    handleCurrentChange(page) {
      this.searchForm.pageIndex = page
      this.getTableData()
    },

    handleSizeChange(size) {
      this.searchForm.pageSize = size
      this.getTableData()
    },

    getSearchBtn() {
      this.searchForm.pageIndex = 1
      this.getTableData()
    },

    resetForm() {
      this.searchForm = {
        pageIndex: 1,
        pageSize: 20,
        docName: '',
        docCode: '',
        docVersion: '',
        docStatus: ''
      }
      this.getTableData()
    },

    async getTableData() {
      this.tableLoading = true
      try {
        const res = await getSopDocList(this.searchForm)
        if (res.success) {
          this.tableData = res.response.data || []
          this.total = res.response.dataCount || 0
        } else {
          this.$message.error(res.msg || '获取数据失败')
        }
      } catch (err) {
        console.error('获取表格数据失败:', err)
        this.$message.error('获取数据失败')
        throw err
      } finally {
        this.tableLoading = false
      }
    },

    // 处理文件下载
    async handleDownload(row) {
      try {
        const res = await downloadSopDoc(row.FileUuid)
        // 创建下载链接
        const blob = new Blob([res], { type: res.type })
        const link = document.createElement('a')
        link.href = window.URL.createObjectURL(blob)
        link.download = row.DocName
        link.click()
        window.URL.revokeObjectURL(link.href)
      } catch (err) {
        console.error('文件下载失败:', err)
        this.$message.error('文件下载失败')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.root-layout {
  height: calc(100% - 60px);
}

.root-content {
  height: 100%;
  padding: 10px;
  display: flex;
  flex-direction: column;

  .InventorySearchBox {
    margin-bottom: 10px;

    .search-form {
      background-color: #f5f7fa;
      padding: 15px;
      border-radius: 4px;

      .form-content {
        .search-area {
          .search-row {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;

            .action-buttons {
              margin-left: auto;
              display: flex;
              gap: 8px;
            }
          }
        }
      }
    }
  }

  .root-main {
    flex: 1;
    overflow: hidden;
  }

  .root-footer {
    padding: 10px 0;
    background-color: #fff;
    border-top: 1px solid #e4e7ed;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .search-row {
    .el-form-item {
      margin-bottom: 10px;
    }
  }
}
</style>
