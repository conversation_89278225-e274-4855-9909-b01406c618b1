{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopAudit\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopAudit\\index.vue", "mtime": 1750252744822}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAkHA;AACA;AACA;AACA;;AAEA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/SOP/sopAudit", "sourcesContent": ["<template>\n  <div class=\"root\">\n    <div class=\"root-head\">\n      <el-form size=\"small\" :inline=\"true\" ref=\"form\" :model=\"searchForm\" @submit.native.prevent>\n        <el-form-item :label=\"$t('SOP.DocName')\" prop=\"docName\">\n          <el-input v-model=\"searchForm.docName\" :placeholder=\"$t('SOP.EnterDocName')\" clearable style=\"width: 200px;\"></el-input>\n        </el-form-item>\n\n        <el-form-item :label=\"$t('SOP.DocCode')\" prop=\"docCode\">\n          <el-input v-model=\"searchForm.docCode\" :placeholder=\"$t('SOP.EnterDocCode')\" clearable style=\"width: 200px;\"></el-input>\n        </el-form-item>\n\n        <el-form-item :label=\"$t('SOP.UploadUser')\" prop=\"uploadUser\">\n          <el-input v-model=\"searchForm.uploadUser\" :placeholder=\"$t('SOP.EnterUploadUser')\" clearable style=\"width: 200px;\"></el-input>\n        </el-form-item>\n\n        <el-form-item class=\"mb-2\">\n          <el-button icon=\"el-icon-search\" @click=\"getSearchBtn()\">{{ $t('GLOBAL._CX') }}</el-button>\n          <el-button icon=\"el-icon-refresh\" @click=\"resetForm()\">{{ $t('GLOBAL._CZ') }}</el-button>\n        </el-form-item>\n      </el-form>\n    </div>\n    <div class=\"root-main\">\n      <el-table class=\"mt-3\"\n                :height=\"mainH\"\n                border\n                :data=\"tableData\"\n                style=\"width: 100%\">\n        <el-table-column\n          type=\"index\"\n          label=\"序号\"\n          width=\"50\"\n          align=\"center\">\n        </el-table-column>\n        <el-table-column v-for=\"(item) in tableName\"\n                         :default-sort=\"{prop: 'date', order: 'descending'}\"\n                         :key=\"item.value\"\n                         :prop=\"item.value\"\n                         :label=\"typeof item.text === 'function' ? item.text() : item.text\"\n                         :width=\"item.width\"\n                         :align=\"item.alignType || 'center'\"\n                         sortable\n                         show-overflow-tooltip\n        >\n          <template slot-scope=\"scope\">\n            <template v-if=\"item.value === 'FileSize'\">\n              {{ formatFileSize(getDocFieldValue(scope.row, item.value)) }}\n            </template>\n            <template v-else-if=\"item.value === 'DocStatus'\">\n              <el-tag :type=\"getStatusType(getDocFieldValue(scope.row, item.value))\" size=\"small\">\n                {{ formatStatus(getDocFieldValue(scope.row, item.value)) }}\n              </el-tag>\n            </template>\n            <template v-else-if=\"item.value === 'OperationType'\">\n              {{ formatOperationType(scope.row[item.value]) }}\n            </template>\n            <template v-else-if=\"item.value === 'AuditResult'\">\n              <el-tag :type=\"getAuditResultType(scope.row[item.value])\" size=\"small\">\n                {{ formatAuditResult(scope.row[item.value]) }}\n              </el-tag>\n            </template>\n            <template v-else-if=\"['DocName', 'DocCode', 'DocVersion', 'FilePath'].includes(item.value)\">\n              {{ getDocFieldValue(scope.row, item.value) }}\n            </template>\n            <template v-else>\n              {{ scope.row[item.value] }}\n            </template>\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"operation\" width=\"180\" :label=\"$t('GLOBAL._ACTIONS')\" align=\"center\">\n          <template slot-scope=\"scope\">\n            <div class=\"operation-buttons\">\n              <el-button size=\"mini\" type=\"primary\" @click=\"previewDoc(scope.row)\">{{ $t('SOP.Preview') }}</el-button>\n              <el-button size=\"mini\" type=\"success\" @click=\"approveAudit(scope.row)\"\n                         :disabled=\"scope.row.AuditResult !== null && scope.row.AuditResult !== 0\">{{ $t('SOP.Approve') }}</el-button>\n              <el-button size=\"mini\" type=\"danger\" @click=\"rejectAudit(scope.row)\"\n                         :disabled=\"scope.row.AuditResult !== null && scope.row.AuditResult !== 0\">{{ $t('SOP.Reject') }}</el-button>\n            </div>\n          </template>\n        </el-table-column>\n      </el-table>\n    </div>\n    <div class=\"root-footer\">\n      <el-pagination\n          class=\"mt-3\"\n          @size-change=\"handleSizeChange\"\n          @current-change=\"handleCurrentChange\"\n          :current-page=\"searchForm.pageIndex\"\n          :page-sizes=\"[10,20, 50, 100,500]\"\n          :page-size=\"searchForm.pageSize\"\n          layout=\"->,total, sizes, prev, pager, next, jumper\"\n          :total=\"total\"\n          background\n      ></el-pagination>\n    </div>\n    <!-- 审核不通过原因对话框 -->\n    <el-dialog :title=\"$t('SOP.RejectReason')\" :visible.sync=\"rejectDialogVisible\" width=\"500px\"\n               :close-on-click-modal=\"false\" :close-on-press-escape=\"false\">\n      <el-form ref=\"rejectForm\" :model=\"rejectForm\" label-width=\"120px\">\n        <el-form-item :label=\"$t('SOP.RejectReason')\" prop=\"auditComment\"\n                      :rules=\"[{ required: true, message: $t('SOP.RejectReasonRequired'), trigger: 'blur' }]\">\n          <el-input type=\"textarea\" v-model=\"rejectForm.auditComment\"\n                    :placeholder=\"$t('SOP.EnterRejectReason')\" :rows=\"4\"></el-input>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"rejectDialogVisible = false\">{{ $t('GLOBAL._QX') }}</el-button>\n        <el-button type=\"primary\" @click=\"confirmReject\">{{ $t('GLOBAL._QD') }}</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport '@/views/Inventory/mystyle.scss';\nimport {\n    delSopAudit, getSopAuditList, saveSopAuditForm\n} from \"@/api/SOP/sopAudit\";\n\nimport { sopAuditColumn } from '@/api/SOP/sopAudit.js';\n\n\nexport default {\n  name: 'index.vue',\n  components: {\n    FormDialog,\n  },\n  data() {\n    return {\n      searchForm: {\n        pageIndex: 1,\n        pageSize: 20,\n        docName: '',\n        docCode: '',\n        uploadUser: ''\n      },\n      total: 0,\n      tableData: [],\n      tableName: sopAuditColumn,\n      loading: false,\n      mainH: 0,\n      buttonOption:{\n        name:'SOP审核',\n        serveIp:'baseURL_DFM',\n        uploadUrl:'/api/SopAudit/ImportData', //导入\n        exportUrl:'/api/SopAudit/ExportData', //导出\n        DownLoadUrl:'/api/SopAudit/DownLoadTemplate', //下载模板\n      },\n      // 审核不通过原因对话框\n      rejectDialogVisible: false,\n      rejectForm: {\n        auditComment: ''\n      },\n      currentAuditRow: null\n    }\n  },\n  mounted() {\n    this.getTableData()\n    this.$nextTick(() => {\n      this.mainH = this.$webHeight(document.getElementsByClassName('root-head')[0].clientHeight, document.getElementsByClassName('root')[0].clientHeight)\n    })\n    window.onresize = () => {\n      this.mainH = this.$webHeight(document.getElementsByClassName('root-head')[0].clientHeight, document.getElementsByClassName('root')[0].clientHeight)\n    }\n  },\n  methods: {\n    showDialog(row) {\n      this.$refs.formDialog.show(row)\n    },\n    handleCurrentChange(page) {\n      this.searchForm.pageIndex = page\n      this.getTableData()\n    },\n    handleSizeChange(size) {\n      this.searchForm.pageSize = size\n      this.getTableData()\n    },\n    getSearchBtn() {\n      this.searchForm.pageIndex = 1\n      this.getTableData()\n    },\n\n    resetForm() {\n      this.searchForm = {\n        pageIndex: 1,\n        pageSize: 20,\n        docName: '',\n        docCode: '',\n        uploadUser: ''\n      }\n      this.getTableData()\n    },\n\n    delRow(row) {\n      this.$confirms({\n        title: this.$t('GLOBAL._TS'),\n        message: this.$t('GLOBAL._COMFIRM'),\n        confirmText: this.$t('GLOBAL._QD'),\n        cancelText: this.$t('GLOBAL._QX')\n      }).then(async () => {\n        delSopAudit([row.ID]).then(res => {\n          this.$message.success(res.msg)\n          this.getTableData()\n        })\n      }).catch(err => {\n        console.log(err);\n      });\n    },\n\n    getTableData() {\n      getSopAuditList(this.searchForm).then(res => {\n        this.tableData = res.response.data\n        this.total = res.response.dataCount\n      })\n    },\n\n    // 获取Doc字段中的文档信息\n    getDocFieldValue(row, fieldName) {\n      // 如果是文档相关字段，从Doc对象中获取\n      if (['DocName', 'DocCode', 'DocVersion', 'FilePath', 'FileSize', 'DocStatus'].includes(fieldName)) {\n        return row.Doc ? row.Doc[fieldName] : ''\n      }\n      // 其他字段直接从row中获取\n      return row[fieldName]\n    },\n\n    // 预览文档\n    previewDoc(row) {\n      // 这里可以实现文档预览功能\n      this.$message.info(this.$t('SOP.PreviewNotImplemented'))\n    },\n\n    // 通过审核\n    approveAudit(row) {\n      this.$confirm(this.$t('SOP.ConfirmApprove'), this.$t('GLOBAL._TS'), {\n        confirmButtonText: this.$t('GLOBAL._QD'),\n        cancelButtonText: this.$t('GLOBAL._QX'),\n        type: 'warning'\n      }).then(() => {\n        const auditData = {\n          ...row,\n          AuditResult: 2, // 审批通过\n          AuditUserId: this.$store.getters.name,\n          AuditComment: this.$t('SOP.AuditPassed')\n        }\n        saveSopAuditForm(auditData).then(res => {\n          this.$message.success(this.$t('SOP.ApproveSuccess'))\n          this.getTableData()\n        })\n      }).catch(() => {\n        this.$message.info(this.$t('GLOBAL._QXCZ'))\n      })\n    },\n\n    // 不通过审核\n    rejectAudit(row) {\n      this.currentAuditRow = row\n      this.rejectForm.auditComment = ''\n      this.rejectDialogVisible = true\n    },\n\n    // 确认不通过\n    confirmReject() {\n      this.$refs.rejectForm.validate((valid) => {\n        if (valid) {\n          const auditData = {\n            ...this.currentAuditRow,\n            AuditResult: 3, // 审批不通过\n            AuditUserId: this.$store.getters.name,\n            AuditComment: this.rejectForm.auditComment\n          }\n          saveSopAuditForm(auditData).then(res => {\n            this.$message.success(this.$t('SOP.RejectSuccess'))\n            this.rejectDialogVisible = false\n            this.getTableData()\n          })\n        }\n      })\n    },\n\n    // 格式化文件大小\n    formatFileSize(size) {\n      if (!size) return ''\n      const units = ['B', 'KB', 'MB', 'GB']\n      let index = 0\n      while (size >= 1024 && index < units.length - 1) {\n        size /= 1024\n        index++\n      }\n      return `${size.toFixed(2)} ${units[index]}`\n    },\n\n    // 格式化文档状态\n    formatStatus(status) {\n      const statusMap = {\n        1: this.$t('SOP.StatusValid'),\n        0: this.$t('SOP.StatusInvalid')\n      }\n      return statusMap[status] || this.$t('GLOBAL._WZ')\n    },\n\n    // 获取状态类型\n    getStatusType(status) {\n      return status === 1 ? 'success' : 'danger'\n    },\n\n    // 格式化操作类型\n    formatOperationType(type) {\n      const typeMap = {\n        1: this.$t('SOP.OperationCreate'),\n        2: this.$t('SOP.OperationModify'),\n        3: this.$t('SOP.OperationDelete')\n      }\n      return typeMap[type] || this.$t('GLOBAL._WZ')\n    },\n\n    // 格式化审核结果\n    formatAuditResult(result) {\n      const resultMap = {\n        0: this.$t('SOP.AuditPending'),\n        1: this.$t('SOP.AuditInProgress'),\n        2: this.$t('SOP.AuditApproved'),\n        3: this.$t('SOP.AuditRejected')\n      }\n      return resultMap[result] || this.$t('GLOBAL._WZ')\n    },\n\n    // 获取审核结果类型\n    getAuditResultType(result) {\n      const typeMap = {\n        0: 'warning',\n        1: 'primary',\n        2: 'success',\n        3: 'danger'\n      }\n      return typeMap[result] || 'info'\n    }\n  }\n}\n\n//<!-- 移到到src/local/en.json和zh-Hans.json -->\n//\"SopAudit\": {\n//    \"table\": {\n//        \"docId\": \"docId\",\n//        \"operationType\": \"operationType\",\n//        \"oldValue\": \"oldValue\",\n//        \"newValue\": \"newValue\",\n//        \"operatorId\": \"operatorId\",\n//        \"operateTime\": \"operateTime\",\n//        \"clientIp\": \"clientIp\",\n//        \"createdate\": \"createdate\",\n//        \"createuserid\": \"createuserid\",\n//        \"modifydate\": \"modifydate\",\n//        \"modifyuserid\": \"modifyuserid\",\n//        \"deleted\": \"deleted\",\n//    }\n//},\n</script>\n\n<style lang=\"scss\" scoped>\n.el-form-item--small.el-form-item {\n  margin-bottom: 0px;\n}\n\n.mt-8p {\n  margin-top: 8px;\n}\n\n.pd-left {\n  padding-left: 5px\n}\n\n.operation-buttons {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  gap: 4px;\n\n  .el-button--mini {\n    padding: 4px 8px;\n    font-size: 11px;\n    border-radius: 3px;\n    min-width: auto;\n  }\n}\n</style>"]}]}