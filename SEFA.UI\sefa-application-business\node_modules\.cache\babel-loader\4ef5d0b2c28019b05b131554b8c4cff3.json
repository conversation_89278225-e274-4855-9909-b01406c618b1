{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopDocSearch\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopDocSearch\\index.vue", "mtime": 1750253234790}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\babel.config.js", "mtime": 1742799047338}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAoGA;AACA,SACAA,aADA,EACAC,cADA,QAEA,kBAFA;AAGA;AAEA;EACAC,iBADA;EAEAC,cAFA;;EAIAC;IACA;MACAC;QACAC,YADA;QAEAC,YAFA;QAGAC,WAHA;QAIAC,WAJA;QAKAC,cALA;QAMAC;MANA,CADA;MASAC,QATA;MAUAC,aAVA;MAWAC,wBAXA;MAYAC,mBAZA;MAaAC;IAbA;EAeA,CApBA;;EAqBA;IACA;MACA;MACA;IACA,CAHA,CAGA;MACAC;MACA;IACA,CANA,SAMA;MACA;IACA;EACA,CA/BA;;EAgCAC;IACAC;EACA,CAlCA;;EAmCAC;IACA;IACAC;MACA;MACA;MACA;MACA;;MACA;QACAC;QACAC;MACA;;MACA;IACA,CAZA;;IAcA;IACAC;MACA;QACA;UAAA;;QACA;UAAA;;QACA;UAAA;;QACA;UAAA;MAJA;IAMA,CAtBA;;IAwBA;IACAC;MACA;QACA;UAAA;;QACA;UAAA;;QACA;UAAA;;QACA;UAAA;MAJA;IAMA,CAhCA;;IAkCAC;MACA;MACA;IACA,CArCA;;IAuCAC;MACA;MACA;IACA,CA1CA;;IA4CAC;MACA;MACA;IACA,CA/CA;;IAiDAC;MACA;QACAvB,YADA;QAEAC,YAFA;QAGAC,WAHA;QAIAC,WAJA;QAKAC,cALA;QAMAC;MANA;MAQA;IACA,CA3DA;;IA6DA;MACA;;MACA;QACA;;QACA;UACA;UACA;QACA,CAHA,MAGA;UACA;QACA;MACA,CARA,CAQA;QACAM;QACA;QACA;MACA,CAZA,SAYA;QACA;MACA;IACA,CA9EA;;IAgFA;IACA;MACA;QACA,+CADA,CAEA;;QACA;UAAAa;QAAA;QACA;QACAC;QACAA;QACAA;QACAZ;MACA,CATA,CASA;QACAF;QACA;MACA;IACA;;EA/FA;AAnCA", "names": ["getSopDocList", "downloadSopDoc", "name", "components", "data", "searchForm", "pageIndex", "pageSize", "doc<PERSON>ame", "docCode", "docVersion", "doc<PERSON><PERSON>us", "total", "tableData", "tableName", "tableLoading", "initLoading", "console", "<PERSON><PERSON><PERSON><PERSON>", "window", "methods", "formatFileSize", "fileSize", "index", "getStatusType", "formatStatus", "handleCurrentChange", "handleSizeChange", "getSearchBtn", "resetForm", "type", "link"], "sourceRoot": "src/views/SOP/sopDocSearch", "sources": ["index.vue"], "sourcesContent": ["<template>\n  <div class=\"root usemystyle\">\n    <div class=\"root-layout\" v-loading=\"initLoading\">\n      <div class=\"root-content\">\n        <div class=\"InventorySearchBox\">\n          <div class=\"search-form\">\n            <el-form size=\"small\" :inline=\"true\" ref=\"form\" :model=\"searchForm\" @submit.native.prevent>\n              <div class=\"form-content\">\n                <div class=\"search-area\">\n                  <div class=\"search-row\">\n                    <el-form-item :label=\"$t('SOP.DocName')\" prop=\"docName\" label-width=\"60px\">\n                      <el-input v-model=\"searchForm.docName\" :placeholder=\"$t('SOP.EnterDocName')\" clearable size=\"small\" style=\"width: 180px;\">\n                      </el-input>\n                    </el-form-item>\n                    <el-form-item :label=\"$t('SOP.DocCode')\" prop=\"docCode\" label-width=\"60px\">\n                      <el-input v-model=\"searchForm.docCode\" :placeholder=\"$t('SOP.EnterDocCode')\" clearable size=\"small\" style=\"width: 120px;\"></el-input>\n                    </el-form-item>\n                    <el-form-item :label=\"$t('SOP.DocVersion')\" prop=\"docVersion\" label-width=\"60px\">\n                      <el-input v-model=\"searchForm.docVersion\" placeholder=\"输入版本\" clearable size=\"small\" style=\"width: 80px;\"></el-input>\n                    </el-form-item>\n                    <el-form-item :label=\"$t('SOP.DocStatus')\" prop=\"docStatus\" label-width=\"40px\">\n                      <el-select v-model=\"searchForm.docStatus\" placeholder=\"选择\" clearable size=\"small\" style=\"width: 70px;\">\n                        <el-option :label=\"$t('SOP.StatusValid')\" :value=\"1\"></el-option>\n                        <el-option :label=\"$t('SOP.StatusInvalid')\" :value=\"0\"></el-option>\n                      </el-select>\n                    </el-form-item>\n                    <div class=\"action-buttons\">\n                      <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"getSearchBtn()\" size=\"small\">{{ $t('GLOBAL._CX') }}</el-button>\n                      <el-button size=\"small\" icon=\"el-icon-refresh\" @click=\"resetForm\">{{ $t('GLOBAL._CZ') }}</el-button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </el-form>\n          </div>\n        </div>\n        <div class=\"root-main\">\n          <el-table class=\"mt-3\"\n                    :height=\"700\"\n                    border\n                    v-loading=\"tableLoading\"\n                    :data=\"tableData\"\n                    style=\"width: 100%; border-radius: 4px;\"\n                    :empty-text=\"'暂无数据'\">\n            <el-table-column\n              type=\"index\"\n              label=\"序号\"\n              width=\"50\"\n              align=\"center\">\n            </el-table-column>\n            <el-table-column v-for=\"(item) in tableName\"\n                             :default-sort=\"{prop: item.value, order: 'descending'}\"\n                             :key=\"item.value\"\n                             :prop=\"item.value\"\n                             :label=\"typeof item.text === 'function' ? item.text() : item.text\"\n                             :width=\"item.width\"\n                             :align=\"item.alignType || 'center'\"\n                             sortable\n                             show-overflow-tooltip>\n              <template slot-scope=\"scope\">\n                <template v-if=\"item.value === 'FileSize'\">\n                  {{ formatFileSize(scope.row[item.value]) }}\n                </template>\n                <template v-else-if=\"item.value === 'DocStatus'\">\n                  <el-tag :type=\"getStatusType(scope.row[item.value])\" size=\"small\">\n                    {{ formatStatus(scope.row[item.value]) }}\n                  </el-tag>\n                </template>\n                <template v-else>\n                  {{ scope.row[item.value] }}\n                </template>\n              </template>\n            </el-table-column>\n            <el-table-column prop=\"operation\" :min-width=\"120\" :label=\"$t('GLOBAL._ACTIONS')\" align=\"center\">\n              <template slot-scope=\"scope\">\n                <el-button size=\"mini\" type=\"text\" @click=\"handleDownload(scope.row)\">{{ $t('SOP.Preview') }}</el-button>\n                <el-button size=\"mini\" type=\"text\" @click=\"handleDownload(scope.row)\">{{ $t('GLOBAL.Download') }}</el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n        </div>\n        <div class=\"root-footer\">\n          <el-pagination\n              class=\"mt-3\"\n              @size-change=\"handleSizeChange\"\n              @current-change=\"handleCurrentChange\"\n              :current-page=\"searchForm.pageIndex\"\n              :page-sizes=\"[10,20, 50, 100,500]\"\n              :page-size=\"searchForm.pageSize\"\n              layout=\"->,total, sizes, prev, pager, next, jumper\"\n              :total=\"total\"\n              background\n          ></el-pagination>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport '@/views/Inventory/mystyle.scss'\nimport {\n    getSopDocList, downloadSopDoc\n} from \"@/api/SOP/sopDoc\";\nimport { sopDocColumns } from '@/columns/SOP/sopDoc.js';\n\nexport default {\n  name: 'index.vue',\n  components: {\n  },\n  data() {\n    return {\n      searchForm: {\n        pageIndex: 1,\n        pageSize: 20,\n        docName: '',\n        docCode: '',\n        docVersion: '',\n        docStatus: ''\n      },\n      total: 0,\n      tableData: [],\n      tableName: sopDocColumns,\n      tableLoading: false,\n      initLoading: false\n    }\n  },\n  async mounted() {\n    try {\n      this.initLoading = true\n      await this.getTableData()\n    } catch (err) {\n      console.error('页面初始化失败:', err)\n      this.$message.error('页面初始化失败，请刷新重试')\n    } finally {\n      this.initLoading = false\n    }\n  },\n  beforeDestroy() {\n    window.onresize = null\n  },\n  methods: {\n    // 格式化文件大小\n    formatFileSize(size) {\n      if (!size) return '0 B'\n      const units = ['B', 'KB', 'MB', 'GB', 'TB']\n      let index = 0\n      let fileSize = parseFloat(size)\n      while (fileSize >= 1024 && index < units.length - 1) {\n        fileSize /= 1024\n        index++\n      }\n      return `${fileSize.toFixed(2)} ${units[index]}`\n    },\n\n    // 获取状态对应的类型\n    getStatusType(status) {\n      switch (status) {\n        case 1: return 'success'\n        case 2: return 'warning'\n        case 0: return 'info'\n        default: return ''\n      }\n    },\n\n    // 格式化状态\n    formatStatus(status) {\n      switch (status) {\n        case 1: return this.$t('SOP.StatusValid')\n        case 2: return this.$t('SOP.AuditPending')\n        case 0: return this.$t('SOP.StatusInvalid')\n        default: return this.$t('GLOBAL._WZ')\n      }\n    },\n\n    handleCurrentChange(page) {\n      this.searchForm.pageIndex = page\n      this.getTableData()\n    },\n\n    handleSizeChange(size) {\n      this.searchForm.pageSize = size\n      this.getTableData()\n    },\n\n    getSearchBtn() {\n      this.searchForm.pageIndex = 1\n      this.getTableData()\n    },\n\n    resetForm() {\n      this.searchForm = {\n        pageIndex: 1,\n        pageSize: 20,\n        docName: '',\n        docCode: '',\n        docVersion: '',\n        docStatus: ''\n      }\n      this.getTableData()\n    },\n\n    async getTableData() {\n      this.tableLoading = true\n      try {\n        const res = await getSopDocList(this.searchForm)\n        if (res.success) {\n          this.tableData = res.response.data || []\n          this.total = res.response.dataCount || 0\n        } else {\n          this.$message.error(res.msg || '获取数据失败')\n        }\n      } catch (err) {\n        console.error('获取表格数据失败:', err)\n        this.$message.error('获取数据失败')\n        throw err\n      } finally {\n        this.tableLoading = false\n      }\n    },\n\n    // 处理文件下载\n    async handleDownload(row) {\n      try {\n        const res = await downloadSopDoc(row.FileUuid)\n        // 创建下载链接\n        const blob = new Blob([res], { type: res.type })\n        const link = document.createElement('a')\n        link.href = window.URL.createObjectURL(blob)\n        link.download = row.DocName\n        link.click()\n        window.URL.revokeObjectURL(link.href)\n      } catch (err) {\n        console.error('文件下载失败:', err)\n        this.$message.error('文件下载失败')\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.root-layout {\n  height: calc(100% - 60px);\n}\n\n.root-content {\n  height: 100%;\n  padding: 10px;\n  display: flex;\n  flex-direction: column;\n\n  .InventorySearchBox {\n    margin-bottom: 10px;\n\n    .search-form {\n      background-color: #f5f7fa;\n      padding: 15px;\n      border-radius: 4px;\n\n      .form-content {\n        .search-area {\n          .search-row {\n            display: flex;\n            align-items: center;\n            flex-wrap: wrap;\n            gap: 10px;\n\n            .action-buttons {\n              margin-left: auto;\n              display: flex;\n              gap: 8px;\n            }\n          }\n        }\n      }\n    }\n  }\n\n  .root-main {\n    flex: 1;\n    overflow: hidden;\n  }\n\n  .root-footer {\n    padding: 10px 0;\n    background-color: #fff;\n    border-top: 1px solid #e4e7ed;\n  }\n}\n\n// 响应式设计\n@media (max-width: 1200px) {\n  .search-row {\n    .el-form-item {\n      margin-bottom: 10px;\n    }\n  }\n}\n</style>\n"]}]}