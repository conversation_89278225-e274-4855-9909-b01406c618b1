{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopDocSearch\\index.vue?vue&type=style&index=0&id=3ad44ce4&lang=scss&scoped=true&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopDocSearch\\index.vue", "mtime": 1750249347904}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1743379022465}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1743379015507}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1743379017016}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1743379015023}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5yb290LWxheW91dCB7CiAgaGVpZ2h0OiBjYWxjKDEwMCUgLSA2MHB4KTsKfQoKLnJvb3QtbGVmdCB7CiAgaGVpZ2h0OiAxMDAlOwogIHBhZGRpbmc6IDEwcHg7CiAgb3ZlcmZsb3c6IGF1dG87CiAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjdmYTsKICBib3JkZXItcmFkaXVzOiA0cHg7CgogIC50cmVlLXRvb2xiYXIgewogICAgbWFyZ2luLWJvdHRvbTogMTBweDsKICAgIGRpc3BsYXk6IGZsZXg7CiAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47CiAgfQoKICAuY3VzdG9tLXRyZWUtbm9kZSB7CiAgICBmbGV4OiAxOwogICAgZGlzcGxheTogZmxleDsKICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47CiAgICBmb250LXNpemU6IDE0cHg7CiAgICBwYWRkaW5nLXJpZ2h0OiA4cHg7CiAgICB3aWR0aDogMTAwJTsKICAgIC50cmVlLXRpdGxlIHsKICAgICAgZm9udC13ZWlnaHQ6IDUwMDsKICAgIH0KICB9CgogIC50cmVlLW5vZGUtYWN0aW9ucyB7CiAgICBkaXNwbGF5OiBub25lOwogIH0KCiAgLmVsLXRyZWUtbm9kZV9fY29udGVudDpob3ZlciB7CiAgICAudHJlZS1ub2RlLWFjdGlvbnMgewogICAgICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7CiAgICB9CiAgfQp9Cgoucm9vdC1yaWdodCB7CiAgcGFkZGluZzogMCAxMnB4OwogIGhlaWdodDogMTAwJTsKICBvdmVyZmxvdzogYXV0bzsKCiAgLkludmVudG9yeVNlYXJjaEJveCB7CiAgICBiYWNrZ3JvdW5kOiAjZmZmOwogICAgcGFkZGluZzogOHB4OwogICAgYm9yZGVyLXJhZGl1czogNHB4OwogICAgYm94LXNoYWRvdzogMCAxcHggNHB4IHJnYmEoMCwgMCwgMCwgMC4wNSk7CiAgICBtYXJnaW4tYm90dG9tOiA4cHg7CgogICAgLnNlYXJjaC1mb3JtIHsKICAgICAgOmRlZXAoLmVsLWZvcm0pIHsKICAgICAgICAuZWwtZm9ybS1pdGVtLS1zbWFsbC5lbC1mb3JtLWl0ZW0gewogICAgICAgICAgbWFyZ2luLWJvdHRvbTogMDsKICAgICAgICB9CiAgICAgIH0KCiAgICAgIC5mb3JtLWNvbnRlbnQgewogICAgICAgIHBhZGRpbmc6IDRweDsKICAgICAgICAKICAgICAgICAuc2VhcmNoLWFyZWEgewogICAgICAgICAgLnNlYXJjaC1yb3cgewogICAgICAgICAgICBkaXNwbGF5OiBmbGV4OwogICAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAgICAgICAgICBnYXA6IDRweDsKCiAgICAgICAgICAgIC5lbC1mb3JtLWl0ZW0gewogICAgICAgICAgICAgIG1hcmdpbjogMDsKICAgICAgICAgICAgICBmbGV4OiBub25lOwogICAgICAgICAgICAgIAogICAgICAgICAgICAgIC5lbC1mb3JtLWl0ZW1fX2xhYmVsIHsKICAgICAgICAgICAgICAgIHBhZGRpbmctcmlnaHQ6IDRweDsKICAgICAgICAgICAgICAgIGxpbmUtaGVpZ2h0OiAyNnB4OwogICAgICAgICAgICAgICAgZm9udC1zaXplOiAxMnB4OwogICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgLmVsLWZvcm0taXRlbV9fY29udGVudCB7CiAgICAgICAgICAgICAgICBsaW5lLWhlaWdodDogMjZweDsKCiAgICAgICAgICAgICAgICAuZWwtaW5wdXQsCiAgICAgICAgICAgICAgICAuZWwtc2VsZWN0IHsKICAgICAgICAgICAgICAgICAgOmRlZXAoLmVsLWlucHV0X19pbm5lcikgewogICAgICAgICAgICAgICAgICAgIGhlaWdodDogMjZweDsKICAgICAgICAgICAgICAgICAgICBsaW5lLWhlaWdodDogMjZweDsKICAgICAgICAgICAgICAgICAgICBwYWRkaW5nOiAwIDhweDsKICAgICAgICAgICAgICAgICAgICBmb250LXNpemU6IDEycHg7CiAgICAgICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgICAgIDpkZWVwKC5lbC1pbnB1dC1ncm91cF9fYXBwZW5kKSB7CiAgICAgICAgICAgICAgICAgICAgcGFkZGluZzogMDsKICAgICAgICAgICAgICAgICAgICAuZWwtYnV0dG9uIHsKICAgICAgICAgICAgICAgICAgICAgIHBhZGRpbmc6IDAgMTBweDsKICAgICAgICAgICAgICAgICAgICAgIGhlaWdodDogMjZweDsKICAgICAgICAgICAgICAgICAgICAgIGJvcmRlcjogbm9uZTsKICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0KCiAgICAgICAgICAgIC5lbC1idXR0b24gewogICAgICAgICAgICAgIGhlaWdodDogMjZweDsKICAgICAgICAgICAgICBwYWRkaW5nOiAwIDEwcHg7CiAgICAgICAgICAgICAgZm9udC1zaXplOiAxMnB4OwogICAgICAgICAgICAgIG1hcmdpbi1sZWZ0OiA0cHg7CiAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgW2NsYXNzXj0iZWwtaWNvbi0iXSB7CiAgICAgICAgICAgICAgICBtYXJnaW4tcmlnaHQ6IDNweDsKICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMTJweDsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0KICAgIH0KICB9CgogIC5yb290LW1haW4gewogICAgbWFyZ2luLXRvcDogMTJweDsKCiAgICAuZWwtdGFibGUgewogICAgICBib3JkZXItcmFkaXVzOiA0cHg7CiAgICAgIG92ZXJmbG93OiBoaWRkZW47CiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAseA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/SOP/sopDoc", "sourcesContent": ["<template>\n  <div class=\"root usemystyle\">\n    <div class=\"root-layout\" v-loading=\"initLoading\">\n      <split-pane \n        :min-percent=\"15\" \n        :max-percent=\"40\" \n        :default-percent=\"20\" \n        split=\"vertical\">\n        <template slot=\"pane1\">\n          <div class=\"root-left\">\n            <div class=\"tree-toolbar\">\n              <el-button-group>\n                <el-button \n                  size=\"small\" \n                  type=\"primary\"\n                  icon=\"el-icon-plus\"\n                  @click=\"addSopDirChild({})\">新建</el-button>\n                <el-button \n                  size=\"small\"\n                  icon=\"el-icon-refresh\"\n                  @click=\"getDirTree\">刷新</el-button>\n                <el-button \n                  size=\"small\"\n                  @click=\"expandAll\">展开</el-button>\n                <el-button \n                  size=\"small\"\n                  @click=\"collapseAll\">收起</el-button>\n              </el-button-group>\n            </div>\n            <el-tree\n              ref=\"tree\"\n              :data=\"treeData\"\n              :props=\"defaultProps\"\n              highlight-current\n              @node-click=\"handleNodeClick\"\n              v-loading=\"treeLoading\">\n              <span class=\"custom-tree-node\" slot-scope=\"{ node, data }\">\n                <div style=\"line-height: 22px;\">\n                  <div class=\"tree-title\">{{ node.data.name }}</div>\n                </div>\n                <span class=\"tree-node-actions\">\n                  <el-button type=\"text\" size=\"mini\" @click.stop=\"() => showSopDirDialog(data)\">\n                    <i class=\"el-icon-edit\"></i>\n                  </el-button>\n                  <el-button type=\"text\" size=\"mini\" @click.stop=\"() => addSopDirChild(data)\">\n                    <i class=\"el-icon-plus\"></i>\n                  </el-button>\n                  <el-button type=\"text\" size=\"mini\" @click.stop=\"() => deleteSopDir(data)\">\n                    <i class=\"el-icon-delete\"></i>\n                  </el-button>\n                </span>\n              </span>\n            </el-tree>\n          </div>\n        </template>\n        <template slot=\"pane2\">\n          <div class=\"root-right\">\n            <div class=\"InventorySearchBox\">\n              <div class=\"search-form\">\n                <el-form size=\"small\" :inline=\"true\" ref=\"form\" :model=\"searchForm\" @submit.native.prevent>\n                  <div class=\"form-content\">\n                    <div class=\"search-area\">\n                      <div class=\"search-row\">\n                        <el-form-item label=\"名称\" prop=\"docName\" label-width=\"40px\">\n                          <el-input v-model=\"searchForm.docName\" placeholder=\"输入名称\" clearable size=\"small\" style=\"width: 180px;\">                            \n                          </el-input>\n                        </el-form-item>\n                        <el-form-item label=\"编码\" prop=\"docCode\" label-width=\"40px\">\n                          <el-input v-model=\"searchForm.docCode\" placeholder=\"输入编码\" clearable size=\"small\" style=\"width: 120px;\"></el-input>\n                        </el-form-item>\n                        <el-form-item label=\"版本\" prop=\"docVersion\" label-width=\"40px\">\n                          <el-input v-model=\"searchForm.docVersion\" placeholder=\"输入版本\" clearable size=\"small\" style=\"width: 80px;\"></el-input>\n                        </el-form-item>\n                        <el-form-item label=\"状态\" prop=\"docStatus\" label-width=\"40px\">\n                          <el-select v-model=\"searchForm.docStatus\" placeholder=\"选择\" clearable size=\"small\" style=\"width: 70px;\">\n                            <el-option label=\"有效\" :value=\"1\"></el-option>\n                            <el-option label=\"无效\" :value=\"0\"></el-option>\n                          </el-select>\n                        </el-form-item>\n                        <div class=\"action-buttons\">\n                          <el-button type=\"success\" size=\"small\" icon=\"el-icon-circle-plus-outline\" @click=\"showDialog({})\">新增</el-button>\n                          <el-button slot=\"append\" type=\"primary\" icon=\"el-icon-search\" @click=\"getSearchBtn()\" size=\"small\">查询</el-button>\n                          <el-button size=\"small\" icon=\"el-icon-refresh\" @click=\"resetForm\">重置</el-button>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </el-form>\n              </div>\n            </div>\n            <div class=\"root-main\">\n              <el-table class=\"mt-3\"\n                        :height=\"700\"\n                        border\n                        v-loading=\"tableLoading\"\n                        :data=\"tableData\"\n                        style=\"width: 100%; border-radius: 4px;\"\n                        :empty-text=\"'暂无数据'\">\n                <el-table-column\n                  type=\"index\"\n                  label=\"序号\"\n                  width=\"50\"\n                  align=\"center\">\n                </el-table-column>\n                <el-table-column v-for=\"(item) in tableName\"\n                                 :default-sort=\"{prop: item.value, order: 'descending'}\"\n                                 :key=\"item.value\"\n                                 :prop=\"item.value\"\n                                 :label=\"typeof item.text === 'function' ? item.text() : item.text\"\n                                 :width=\"item.width\"\n                                 :align=\"item.alignType || 'center'\"\n                                 sortable\n                                 show-overflow-tooltip>\n                  <template slot-scope=\"scope\">\n                    <template v-if=\"item.value === 'FileSize'\">\n                      {{ formatFileSize(scope.row[item.value]) }}\n                    </template>\n                    <template v-else-if=\"item.value === 'DocStatus'\">\n                      <el-tag :type=\"getStatusType(scope.row[item.value])\" size=\"small\">\n                        {{ formatStatus(scope.row[item.value]) }}\n                      </el-tag>\n                    </template>\n                    <template v-else>\n                      {{ scope.row[item.value] }}\n                    </template>\n                  </template>\n                </el-table-column>\n                <el-table-column prop=\"operation\" :min-width=\"160\" :label=\"$t('GLOBAL._ACTIONS')\" align=\"center\">\n                  <template slot-scope=\"scope\">\n                    <el-button size=\"mini\" type=\"text\" @click=\"showDialog(scope.row)\">{{ $t('GLOBAL._CK') }}</el-button>\n                    <el-button size=\"mini\" type=\"text\" @click=\"handleDownload(scope.row)\">{{ $t('GLOBAL.Download') }}</el-button>\n                    <el-button size=\"mini\" type=\"text\" @click=\"delRow(scope.row)\">{{ $t('GLOBAL._SC') }}</el-button>\n                    <el-button size=\"mini\" type=\"text\" @click=\"delRow(scope.row)\">{{ $t('GLOBAL._shenqing') }}</el-button>               \n                  </template>\n                </el-table-column>\n              </el-table>\n            </div>\n          </div>\n        </template>\n      </split-pane>\n      <div class=\"root-footer\">\n        <el-pagination\n            class=\"mt-3\"\n            @size-change=\"handleSizeChange\"\n            @current-change=\"handleCurrentChange\"\n            :current-page=\"searchForm.pageIndex\"\n            :page-sizes=\"[10,20, 50, 100,500]\"\n            :page-size=\"searchForm.pageSize\"\n            layout=\"->,total, sizes, prev, pager, next, jumper\"\n            :total=\"total\"\n            background\n        ></el-pagination>\n      </div>\n      <form-dialog @saveForm=\"getSearchBtn\" ref=\"formDialog\" :treeData=\"treeData\"></form-dialog>\n      <sop-dir-form-dialog \n        :visible.sync=\"sopDirDialogVisible\" \n        :form-data=\"sopDirDialogForm\" \n        @saveForm=\"getDirTree\" \n        ref=\"sopDirFormDialog\">\n      </sop-dir-form-dialog>\n    </div>\n  </div>\n</template>\n\n<script>\nimport '@/views/Inventory/mystyle.scss'\nimport FormDialog from './form-dialog'\nimport TreeSelect from '../components/tree-select'\nimport SplitPane from '../components/split-pane'\nimport {\n    delSopDoc, getSopDocList, downloadSopDoc\n} from \"@/api/SOP/sopDoc\";\nimport { getSopDirTree, saveSopDirForm, delSopDir } from \"@/api/SOP/sopDir\";\nimport SopDirFormDialog from \"@/views/SOP/sopDir/form-dialog\";\nimport { sopDocColumns } from '@/columns/SOP/sopDoc.js';\n\nexport default {\n  name: 'index.vue',\n  components: {\n    FormDialog,\n    SopDirFormDialog,\n    SplitPane\n  },\n  data() {\n    return {\n      searchForm: {\n        pageIndex: 1,\n        pageSize: 20,\n        dirId: '',\n        docName: '',\n        docCode: '',\n        docStatus: '',\n        deleted: ''\n      },\n      total: 0,\n      tableData: [],\n      hansObj: this.$t('SOP文档主表.table'),\n      tableName: [],\n      tableLoading: false,\n      treeLoading: false,\n      initLoading: false,\n      tableOption: [],\n      mainH: 0,\n      buttonOption: {\n        name:'SOP文档主表',\n        serveIp:'baseURL_DFM',\n        uploadUrl:'/api/SopDoc/ImportData',\n        exportUrl:'/api/SopDoc/ExportData',\n        DownLoadUrl:'/api/SopDoc/DownLoadTemplate',\n      },\n      treeData: [],\n      defaultProps: {\n        children: 'children',\n        label:  function (node){\n          return node.data.DirName\n        } \n      },\n      docStatusOptions: [],\n      deletedOptions: [],\n      sopDirDialogVisible: false,\n      sopDirDialogForm: {},\n      sopDirFormLoading: false\n    }\n  },\n  async mounted() {\n    try {\n      this.initLoading = true\n      this.getZHHans()\n      await Promise.all([\n        this.getDictData('docStatus', 'docStatusOptions'),\n        this.getDictData('deletedStatus', 'deletedOptions')\n      ])\n      await this.getDirTree()\n      await this.getTableData()\n      this.$nextTick(() => {\n        this.mainH = this.$webHeight(\n          document.getElementsByClassName('root')[0].clientHeight,\n          document.getElementsByClassName('root')[0].clientHeight\n        )\n      })\n    } catch (err) {\n      console.error('页面初始化失败:', err)\n      this.$message.error('页面初始化失败，请刷新重试')\n    } finally {\n      this.initLoading = false\n    }\n  },\n  beforeDestroy() {\n    window.onresize = null\n  },\n  methods: {\n    // 格式化文件大小\n    formatFileSize(size) {\n      if (!size) return '0 B'\n      const units = ['B', 'KB', 'MB', 'GB', 'TB']\n      let index = 0\n      let fileSize = parseFloat(size)\n      while (fileSize >= 1024 && index < units.length - 1) {\n        fileSize /= 1024\n        index++\n      }\n      return `${fileSize.toFixed(2)} ${units[index]}`\n    },\n\n    // 获取状态对应的类型\n    getStatusType(status) {\n      switch (status) {\n        case 1: return 'success'\n        case 2: return 'warning'\n        case 0: return 'info'\n        default: return ''\n      }\n    },\n\n    // 格式化状态\n    formatStatus(status) {\n      switch (status) {\n        case 1: return this.$t('SOP.StatusValid')\n        case 2: return this.$t('SOP.AuditPending')\n        case 0: return this.$t('SOP.StatusInvalid')\n        default: return this.$t('GLOBAL._WZ')\n      }\n    },\n\n    async getDictData(dictType, targetKey) {\n      try {\n        this[targetKey] = await this.$getNewDataDictionary(dictType)\n      } catch (err) {\n        console.error(`获取${dictType}字典数据失败:`, err)\n        this.$message.error('获取字典数据失败')\n        throw err\n      }\n    },\n\n    getZHHans() {\n      this.tableName = sopDocColumns\n    },\n\n    showDialog(row) {\n      this.$refs.formDialog.show(row)\n    },\n\n    handleCurrentChange(page) {\n      this.searchForm.pageIndex = page\n      this.getTableData()\n    },\n\n    handleSizeChange(size) {\n      this.searchForm.pageSize = size\n      this.getTableData()\n    },\n\n    getSearchBtn() {\n      this.searchForm.pageIndex = 1\n      this.getTableData()\n    },\n\n    resetForm() {\n      this.searchForm = {\n        pageIndex: 1,\n        pageSize: 20,\n        dirId: '',\n        docName: '',\n        docCode: '',\n        docStatus: '',\n        deleted: ''\n      }\n      this.getTableData()\n    },\n\n    async delRow(row) {\n      try {\n        await this.$confirms({\n          title: this.$t('GLOBAL._TS'),\n          message: this.$t('GLOBAL._COMFIRM'),\n          confirmText: this.$t('GLOBAL._QD'),\n          cancelText: this.$t('GLOBAL._QX')\n        })\n        const res = await delSopDoc([row.ID])\n        if (res.success) {\n          this.$message.success(res.msg || '删除成功')\n          this.getTableData()\n        } else {\n          this.$message.error(res.msg || '删除失败')\n        }\n      } catch (err) {\n        if (err) {\n          console.error('删除数据失败:', err)\n          this.$message.error('删除失败')\n        }\n      }\n    },\n\n    async getTableData(data) {\n      this.tableLoading = true\n      try {\n        const res = await getSopDocList(this.searchForm)\n        if (res.success) {\n          this.tableData = res.response.data || []\n          this.total = res.response.dataCount || 0\n        } else {\n          this.$message.error(res.msg || '获取数据失败')\n        }\n      } catch (err) {\n        console.error('获取表格数据失败:', err)\n        this.$message.error('获取数据失败')\n        throw err\n      } finally {\n        this.tableLoading = false\n      }\n    },\n    \n    async getDirTree() {\n      this.treeLoading = true\n      try {\n        const res = await getSopDirTree()\n        if (res.success) {\n          this.treeData = res.response || []\n        } else {\n          this.$message.error(res.msg || '获取目录树失败')\n        }\n      } catch (err) {\n        console.error('获取目录树失败:', err)\n        this.$message.error('获取目录树失败')\n        throw err\n      } finally {\n        this.treeLoading = false\n      }\n    },\n    \n    handleNodeClick(data) {\n      // 保留分页配置,重置其他搜索条件\n      const { pageIndex, pageSize } = this.searchForm\n      this.searchForm = {\n        pageIndex,\n        pageSize,\n        dirId: data.id,\n        docName: '',\n        docCode: '',\n        docStatus: '',\n        deleted: ''\n      }\n      this.getTableData()\n    },\n    \n    showSopDirDialog(data) {\n      this.sopDirDialogForm = { ...data }\n      this.sopDirDialogVisible = true\n      this.$nextTick(() => {\n        this.$refs.sopDirFormDialog.show(data, 'show')\n      })\n    },\n    \n    addSopDirChild(data) {\n      this.sopDirDialogForm = { parentId: data.id }\n      this.sopDirDialogVisible = true\n      this.$nextTick(() => {\n        this.$refs.sopDirFormDialog.show(data, 'add')\n      })\n    },\n    \n    async deleteSopDir(data) {\n      try {\n        await this.$confirms({\n          title: this.$t('GLOBAL._TS'),\n          message: '确定删除该目录吗？',\n          confirmText: this.$t('GLOBAL._QD'),\n          cancelText: this.$t('GLOBAL._QX')\n        })\n        const res = await delSopDir([data.id])\n        if (res.success) {\n          this.$message.success(res.msg || '删除成功')\n          await this.getDirTree()\n        } else {\n          this.$message.error(res.msg || '删除失败')\n        }\n      } catch (err) {\n        if (err) {\n          console.error('删除目录失败:', err)\n          this.$message.error('删除失败')\n        }\n      }\n    },\n\n    // 展开所有节点\n    expandAll() {\n      this.expandNodes(this.$refs.tree.store.root, true)          \n    },\n\n    // 收起所有节点\n    collapseAll() {\n      this.expandNodes(this.$refs.tree.store.root, false)\n    },\n\n    // 处理文件下载\n    async handleDownload(row) {\n      try {\n        const res = await downloadSopDoc(row.FileUuid)\n        // 创建下载链接\n        const blob = new Blob([res], { type: res.type })\n        // const fileName = row.DocName.split('.').slice(0, -1).join('.') // 移除最后一个扩展名\n        const link = document.createElement('a')\n        link.href = window.URL.createObjectURL(blob)\n        link.download = row.DocName\n        link.click()\n        window.URL.revokeObjectURL(link.href)\n      } catch (err) {\n        console.error('文件下载失败:', err)\n        this.$message.error('文件下载失败')\n      }\n    },\n    //树节点展开关闭\n    expandNodes(node, type){\n      node.expanded = type;\n      for(let i = 0; i<node.childNodes.length; i++){\n        node.childNodes[i].expanded = type;\n        if(node.childNodes[i].childNodes.length > 0){\n          this.expandNodes(node.childNodes[i], type);\n        }\n      }\n    },\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.root-layout {\n  height: calc(100% - 60px);\n}\n\n.root-left {\n  height: 100%;\n  padding: 10px;\n  overflow: auto;\n  background-color: #f5f7fa;\n  border-radius: 4px;\n\n  .tree-toolbar {\n    margin-bottom: 10px;\n    display: flex;\n    justify-content: space-between;\n  }\n\n  .custom-tree-node {\n    flex: 1;\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    font-size: 14px;\n    padding-right: 8px;\n    width: 100%;\n    .tree-title {\n      font-weight: 500;\n    }\n  }\n\n  .tree-node-actions {\n    display: none;\n  }\n\n  .el-tree-node__content:hover {\n    .tree-node-actions {\n      display: inline-block;\n    }\n  }\n}\n\n.root-right {\n  padding: 0 12px;\n  height: 100%;\n  overflow: auto;\n\n  .InventorySearchBox {\n    background: #fff;\n    padding: 8px;\n    border-radius: 4px;\n    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);\n    margin-bottom: 8px;\n\n    .search-form {\n      :deep(.el-form) {\n        .el-form-item--small.el-form-item {\n          margin-bottom: 0;\n        }\n      }\n\n      .form-content {\n        padding: 4px;\n        \n        .search-area {\n          .search-row {\n            display: flex;\n            align-items: center;\n            gap: 4px;\n\n            .el-form-item {\n              margin: 0;\n              flex: none;\n              \n              .el-form-item__label {\n                padding-right: 4px;\n                line-height: 26px;\n                font-size: 12px;\n              }\n\n              .el-form-item__content {\n                line-height: 26px;\n\n                .el-input,\n                .el-select {\n                  :deep(.el-input__inner) {\n                    height: 26px;\n                    line-height: 26px;\n                    padding: 0 8px;\n                    font-size: 12px;\n                  }\n\n                  :deep(.el-input-group__append) {\n                    padding: 0;\n                    .el-button {\n                      padding: 0 10px;\n                      height: 26px;\n                      border: none;\n                    }\n                  }\n                }\n              }\n            }\n\n            .el-button {\n              height: 26px;\n              padding: 0 10px;\n              font-size: 12px;\n              margin-left: 4px;\n              \n              [class^=\"el-icon-\"] {\n                margin-right: 3px;\n                font-size: 12px;\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n\n  .root-main {\n    margin-top: 12px;\n\n    .el-table {\n      border-radius: 4px;\n      overflow: hidden;\n    }\n  }\n}\n</style>\n"]}]}